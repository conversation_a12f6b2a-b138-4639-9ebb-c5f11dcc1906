{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-12-18 05:51:56.388603", "doctype": "DocType", "engine": "InnoDB", "field_order": ["details_tab", "source_material_section", "naming_series", "scan_barcode", "section_break_mlmm", "column_break_4n1k", "serial_no", "column_break_orfc", "item_code", "column_break_6dcm", "material_size", "column_break_xtzj", "warehouse", "column_break_ujki", "basic_rate", "amended_from", "section_break_stdz", "items", "section_break_hcgy", "total_outgoing_value", "column_break_mxlm", "total_incoming_value", "value_difference", "other_info_tab", "section_break_bjus", "repack_stock_entry", "scrap_stock_entry", "connections_tab"], "fields": [{"fieldname": "details_tab", "fieldtype": "Tab Break", "label": "Details"}, {"fieldname": "source_material_section", "fieldtype": "Section Break"}, {"fieldname": "naming_series", "fieldtype": "Select", "hidden": 1, "label": "Series", "options": "VTMC-YY.-.####"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"fieldname": "section_break_mlmm", "fieldtype": "Section Break", "label": "Source Material"}, {"fieldname": "column_break_4n1k", "fieldtype": "Column Break"}, {"fetch_from": "item.serial_no_series", "fieldname": "serial_no", "fieldtype": "Link", "in_list_view": 1, "label": "Serial No", "options": "Serial No", "reqd": 1}, {"fieldname": "column_break_orfc", "fieldtype": "Column Break"}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>"}, {"fieldname": "column_break_6dcm", "fieldtype": "Column Break"}, {"fieldname": "material_size", "fieldtype": "Link", "label": "Material Size", "options": "Velocetec Material Size"}, {"fieldname": "column_break_xtzj", "fieldtype": "Column Break"}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse"}, {"fieldname": "column_break_ujki", "fieldtype": "Column Break"}, {"fieldname": "basic_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Rate"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Velocetec Material Cut", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "section_break_stdz", "fieldtype": "Section Break", "label": "Target Material"}, {"fieldname": "items", "fieldtype": "Table", "label": " Items", "options": "Velocetec Material Cut Item"}, {"fieldname": "section_break_hcgy", "fieldtype": "Section Break"}, {"fieldname": "total_outgoing_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Outgoing Value", "read_only": 1}, {"fieldname": "column_break_mxlm", "fieldtype": "Column Break"}, {"fieldname": "total_incoming_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Incoming Value", "read_only": 1}, {"fieldname": "value_difference", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Value Difference", "read_only": 1}, {"fieldname": "other_info_tab", "fieldtype": "Tab Break", "label": "Other Info"}, {"fieldname": "section_break_bjus", "fieldtype": "Section Break"}, {"fieldname": "repack_stock_entry", "fieldtype": "Link", "label": "Repack Stock Entry", "options": "Stock Entry"}, {"fieldname": "scrap_stock_entry", "fieldtype": "Link", "label": "Scrap Stock Entry", "options": "Stock Entry"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-04-18 03:14:48.627699", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Material Cut", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}