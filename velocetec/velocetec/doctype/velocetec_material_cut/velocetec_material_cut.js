// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

// Global variables for dimension validation
let source_material_dimensions = {};
let material_form = null;
let validation_enabled = true;

frappe.ui.form.on('Velocetec Material Cut', {
    scan_barcode: function (frm) {
        if (frm.doc.scan_barcode) {
            process_scan(frm, frm.doc.scan_barcode);
        }
    },
    serial_no: function (frm) {
        if (frm.doc.serial_no) {
            get_material_details(frm, frm.doc.serial_no);

            // Update all child row serial number placeholders
            if (frm.doc.items) {
                frm.doc.items.forEach(function (row) {
                    frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
                });
            }
        }
    },

    item_code: function (frm) {
        // When parent item_code changes, update all child rows with base item code
        if (frm.doc.item_code && frm.doc.items) {
            // Get base item code (remove dimension suffix if present)
            let base_item_code = get_base_item_code(frm.doc.item_code);

            frm.doc.items.forEach(function (row) {
                frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
            });
        }
    },

    material_size: function (frm) {
        // Load source material dimensions when material size changes
        if (frm.doc.material_size) {
            load_source_material_dimensions(frm);
        }
    },

    basic_rate: function (frm) {
        // When basic rate changes, recalculate child rates
        calculate_child_rates(frm);
    },
    onload: function (frm) {
        frm.set_query('material_size', 'items', function (doc) {
            let source_material_form = null;

            frappe.call({
                method: "frappe.client.get_value",
                args: {
                    doctype: "Item",
                    fieldname: "custom_material_form",
                    filters: { name: doc.item_code }
                },
                async: false,
                callback: function (response) {
                    if (response.message) {
                        source_material_form = response.message.custom_material_form;
                    }
                }
            });
            return {
                filters: {
                    material_form: source_material_form,
                }
            };
        });
        frm.set_df_property('item_code', 'read_only', 1);
        frm.set_df_property('material_size', 'read_only', 1);
        frm.set_df_property('warehouse', 'read_only', 1);
        frm.set_df_property('basic_rate', 'read_only', 1);

        // Material size loaded - no validation needed for sequential cutting
    },

    refresh: function (frm) {
        // No validation needed for sequential cutting

        // Add Material Cutting Dialog button for draft documents
        if (frm.doc.docstatus === 0 && frm.doc.item_code && frm.doc.material_size) {
            frm.add_custom_button(__("Material Cutting Assistant"), function () {
                open_material_cutting_dialog(frm);
            }, __("Tools"));
        }

        // Add view buttons similar to Stock Entry
        if (frm.doc.docstatus > 0) {
            // Add Stock Ledger view button
            frm.add_custom_button(__("Stock Ledger"), function () {
                frappe.route_options = {
                    voucher_no: frm.doc.repack_stock_entry,
                    from_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                    to_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                    company: frm.doc.company || frappe.defaults.get_user_default("Company"),
                    show_cancelled_entries: frm.doc.docstatus === 2,
                    ignore_prepared_report: true
                };
                frappe.set_route("query-report", "Stock Ledger");
            }, __("View"));

            // Add Serial / Batch Nos view button if there are items with serial numbers
            if (frm.doc.items && frm.doc.items.some(item => item.serial_no)) {
                frm.add_custom_button(__("Serial / Batch Nos"), function () {
                    frappe.route_options = {
                        voucher_no: frm.doc.repack_stock_entry,
                        voucher_type: "Stock Entry",
                        from_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                        to_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                        company: frm.doc.company || frappe.defaults.get_user_default("Company")
                    };
                    frappe.set_route("query-report", "Serial and Batch Summary");
                }, __("View"));
            }
        }
    }
});

function get_base_item_code(item_code) {
    // Remove dimension suffix like " - 10x20x21" or " - 15x100"
    // Pattern matches " - " followed by numbers and 'x' characters
    const dimensionPattern = /\s*-\s*\d+x\d+(?:x\d+)?$/;
    return item_code.replace(dimensionPattern, '');
}

function get_material_details(frm, serial_no) {
    frappe.call({
        method: "velocetec.api.utils.get_material_details",
        args: {
            serial_no: serial_no
        },
        callback: function (response) {
            const data = response.message;
            if (data.item_code) {
                frm.set_value('item_code', data.item_code || null);
                frm.set_value('material_size', data.velocetec_material_size || null);
                frm.set_value('warehouse', data.warehouse || null);
                frm.set_value('basic_rate', data.incoming_rate || null);
            } else {
                frappe.msgprint("No material details were found for the given Serial No.");
            }
        }
    });
}

function process_scan(frm, scan_input) {
    frappe.call({
        method: "erpnext.stock.utils.scan_barcode",
        args: {
            search_value: scan_input
        },
        callback: function (response) {
            const data = response.message;
            if (data.serial_no) {
                frm.set_value('serial_no', data.serial_no || null);
                frm.set_value('scan_barcode', '');
            } else {
                frm.set_value('scan_barcode', '');
            }
        }
    });
}

function generate_serial_no_for_row(frm, row) {
    const source_serial_no = frm.doc.serial_no;

    if (!source_serial_no) {
        frappe.msgprint(__('Please select a Source Serial No.'));
        return;
    }

    let base_serial_no = `${source_serial_no}-${row.idx}`;
    const existing_serials_in_child_table = frm.doc.items.map((item) => item.serial_no);


    if (existing_serials_in_child_table.includes(base_serial_no)) {
        let counter = row.idx + 1;
        (function find_next_available_serial() {
            base_serial_no = `${source_serial_no}-${counter}`;
            if (!existing_serials_in_child_table.includes(base_serial_no)) {

                frappe.db.exists('Serial No', base_serial_no).then((exists) => {
                    if (!exists) {
                        frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
                    } else {
                        counter++;
                        find_next_available_serial();
                    }
                });
            } else {
                counter++;
                find_next_available_serial();
            }
        })();
    } else {

        frappe.db.exists('Serial No', base_serial_no).then((exists) => {
            if (!exists) {
                frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
            } else {
                let counter = row.idx + 1;
                (function find_next_available_serial() {
                    base_serial_no = `${source_serial_no}-${counter}`;
                    if (!existing_serials_in_child_table.includes(base_serial_no)) {
                        frappe.db.exists('Serial No', base_serial_no).then((exists) => {
                            if (!exists) {
                                frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
                            } else {
                                counter++;
                                find_next_available_serial();
                            }
                        });
                    } else {
                        counter++;
                        find_next_available_serial();
                    }
                })();
            }
        });
    }
}

function set_target_material_rate(frm, row) {
    frappe.call({
        method: "velocetec.api.utils.calculate_target_material_rate",
        args: {
            target_material_size: row.material_size,
            source_material_size: frm.doc.material_size,
            source_basic_rate: frm.doc.basic_rate,
            item_code: frm.doc.item_code
        },
        callback: function (response) {
            if (response.message && response.message.target_basic_rate) {
                frappe.model.set_value(row.doctype, row.name, 'basic_rate', response.message.target_basic_rate);
            } else {
                frappe.msgprint(__('Unable to calculate the basic rate for the target material size.'));
            }
        }
    });
}

frappe.ui.form.on('Velocetec Material Cut Item', {

    material_size: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];
        // Use base item code (remove dimension suffix if present)
        if (frm.doc.item_code) {
            let base_item_code = get_base_item_code(frm.doc.item_code);
            frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
        }

        // Show placeholder for serial number
        if (frm.doc.serial_no && !row.serial_no) {
            frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
        }

        if (row.material_size && frm.doc.material_size && frm.doc.basic_rate) {
            set_target_material_rate(frm, row);
        }
    },

    basic_rate: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];

        const total_outgoing_value = frm.doc.basic_rate;
        frm.set_value('total_outgoing_value', total_outgoing_value);

        let total_incoming_value = 0;
        frm.doc.items.forEach(item => {
            total_incoming_value = total_incoming_value + item.basic_rate;
        });

        frm.set_value('total_incoming_value', total_incoming_value);

        const value_difference = total_outgoing_value - total_incoming_value;
        frm.set_value('value_difference', value_difference);
        frm.refresh_field('total_incoming_value');
        frm.refresh_field('value_difference');
    },

    // Dimension change triggers - only calculate rates now
    x_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    y_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    z_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    d_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    l_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },

    // Trigger validation when rows are added or removed
    items_add: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];
        if (frm.doc.item_code) {
            // Use base item code (remove dimension suffix if present)
            let base_item_code = get_base_item_code(frm.doc.item_code);
            frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
        }

        // Show placeholder for serial number
        if (frm.doc.serial_no) {
            frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
        }

        // No validation needed for sequential cutting
    },

    items_remove: function (frm, cdt, cdn) {
        // No validation needed for sequential cutting
    }
});


// Custom CSS removed - using standard Frappe UI components


// Old validation functions removed - using sequential cutting now

// Old validation function removed - using sequential cutting now

// Old validation display functions removed

// Old validation functions removed - not needed for sequential cutting

// Old scrap management functions removed - not needed for sequential cutting

// Old create_scrap_entry function removed


// Old validation events removed - using sequential cutting now
frappe.ui.form.on('Velocetec Material Cut Item', {
    x_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    y_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    z_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    d_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    },
    l_dim: function (frm, cdt, cdn) {
        calculate_child_rates(frm);
    }
});

// ============================================================================
// RATE CALCULATION FUNCTIONS
// ============================================================================

function calculate_child_rates(frm) {
    /**
     * Calculate child item rates based on dimensional splitting
     */
    if (!frm.doc.material_size || !frm.doc.items || !frm.doc.basic_rate) {
        return;
    }

    // Call backend to calculate rates
    frappe.call({
        method: "calculate_child_rates",
        doc: frm.doc,
        callback: function (response) {
            if (response.message) {
                // Refresh the child table to show updated rates
                frm.refresh_field('items');

                // Show success message
                frappe.show_alert({
                    message: 'Child rates calculated based on dimensions',
                    indicator: 'green'
                });
            }
        }
    });
}

// ============================================================================
// MATERIAL CUTTING DIALOG
// ============================================================================

function open_material_cutting_dialog(frm) {
    /**
     * Open an interactive material cutting dialog
     */
    if (!frm.doc.material_size || !frm.doc.item_code) {
        frappe.msgprint(__('Please select source material and material size first.'));
        return;
    }

    // Get source material dimensions directly from material size
    frappe.db.get_doc('Velocetec Material Size', frm.doc.material_size)
        .then(material_size_doc => {
            const validation_info = {
                source_dimensions: {
                    material_form: material_size_doc.material_form,
                    x: material_size_doc.x_dim || 0,
                    y: material_size_doc.y_dim || 0,
                    z: material_size_doc.z_dim || 0,
                    d: material_size_doc.d_dim || 0,
                    l: material_size_doc.l_dim || 0
                }
            };
            console.log('Material cutting info:', validation_info);
            show_cutting_dialog(frm, validation_info);
        })
        .catch(error => {
            console.error('Error loading material dimensions:', error);
            frappe.msgprint(__('Unable to load material dimensions.'));
        });
}

function show_cutting_dialog(frm, validation_info) {
    /**
     * Show the material cutting dialog with interactive interface
     */
    if (!validation_info || !validation_info.source_dimensions) {
        frappe.msgprint(__('Invalid material dimensions data.'));
        return;
    }

    const source_dims = validation_info.source_dimensions;
    const material_form = validation_info.source_dimensions.material_form;

    let dialog_html = `
        <style>
            .piece-item {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                cursor: pointer;
                transition: all 0.3s ease;
                background: #fff;
            }
            .piece-item:hover {
                border-color: #007bff;
                box-shadow: 0 2px 8px rgba(0,123,255,0.15);
                transform: translateY(-1px);
            }
            .piece-item.selected {
                border-color: #007bff;
                background: #f8f9ff;
                box-shadow: 0 4px 12px rgba(0,123,255,0.2);
            }
            .piece-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            .piece-number {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .piece-details {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }
            .dimension-display {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .volume-display {
                margin-left: 24px;
            }
            .pieces-container {
                max-height: 400px;
                overflow-y: auto;
            }
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
        </style>
        <div class="material-cutting-dialog">
            <!-- Source Material Info Card -->
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fa fa-cube"></i> Source Material</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">Item Code</small>
                            <div class="font-weight-bold">${frm.doc.item_code}</div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">Serial Number</small>
                            <div class="font-weight-bold">${frm.doc.serial_no || 'Not set'}</div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">Dimensions (${material_form || 'Unknown'})</small>
                            <div class="font-weight-bold text-primary">
                                ${material_form === 'Block' ?
            `${source_dims.x || 0} × ${source_dims.y || 0} × ${source_dims.z || 0} mm` :
            `⌀${source_dims.d || 0} × ${source_dims.l || 0} mm`
        }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Material Pieces Section -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <h6 class="mb-0"><i class="fa fa-scissors"></i> Material Pieces</h6>
                        <small class="text-muted">Click on a piece to select it for cutting</small>
                    </div>
                    <div>
                        <span class="badge badge-info" id="pieces-count">1 piece</span>
                    </div>
                </div>
                <div class="card-body">
                    <div id="current-pieces-list" class="pieces-container">
                        <!-- Current pieces will be shown here -->
                    </div>
                    <div class="text-center mt-3" id="pieces-help" style="display: none;">
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i>
                            Select a piece above and use the cutting controls to split it into smaller pieces
                        </small>
                    </div>
                </div>
            </div>

            <!-- Cutting Controls -->
            <div class="card mt-3" id="cutting-controls" style="display: none;">
                <div class="card-header bg-warning">
                    <h6 class="mb-0"><i class="fa fa-cut"></i> Cut Selected Piece</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <div id="selected-piece-info"></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Cut Dimension</label>
                                <select class="form-control" id="cut-dimension">
                                    <!-- Options will be populated based on material form -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Cut Position (mm)</label>
                                <input type="number" class="form-control" id="cut-position"
                                       placeholder="Enter cut position" step="0.1" min="0.1">
                                <small class="form-text text-muted">Position where to make the cut</small>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <button class="btn btn-secondary mr-2" onclick="window.cancel_cut()">
                            <i class="fa fa-times"></i> Cancel
                        </button>
                        <button class="btn btn-success" onclick="window.execute_cut()">
                            <i class="fa fa-cut"></i> Execute Cut
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    let dialog = new frappe.ui.Dialog({
        title: __('🔧 Material Cutting Assistant'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'cutting_interface',
                options: dialog_html
            }
        ],
        size: 'extra-large',
        primary_action_label: __('✓ Apply All Cuts to Form'),
        primary_action: function () {
            if (current_pieces.length === 0) {
                frappe.msgprint(__('No pieces to apply. Please create some cuts first.'));
                return;
            }

            frappe.confirm(
                __(`Apply ${current_pieces.length} pieces to the form? This will replace any existing items.`),
                function () {
                    apply_cuts_to_form(frm, dialog);
                }
            );
        },
        secondary_action_label: __('Cancel'),
        secondary_action: function () {
            dialog.hide();
        }
    });

    dialog.show();

    // Store dialog reference and validation info globally for helper functions
    window.cutting_dialog = dialog;
    window.cutting_validation_info = validation_info;
    window.cutting_frm = frm;

    // Wait for dialog to be fully rendered before initializing
    setTimeout(() => {
        console.log('Initializing cutting system...');
        // Check if DOM elements exist
        const pieces_list = document.getElementById('current-pieces-list');
        const cutting_controls = document.getElementById('cutting-controls');
        console.log('DOM elements found:', { pieces_list: !!pieces_list, cutting_controls: !!cutting_controls });

        if (pieces_list) {
            // Initialize the cutting system
            initialize_cutting_system(frm, validation_info);
        } else {
            console.error('Required DOM elements not found, retrying...');
            // Try multiple times with increasing delays
            let retryCount = 0;
            const retryInit = () => {
                retryCount++;
                const pieces_list_retry = document.getElementById('current-pieces-list');
                if (pieces_list_retry) {
                    initialize_cutting_system(frm, validation_info);
                } else if (retryCount < 5) {
                    setTimeout(retryInit, 300 * retryCount);
                } else {
                    console.error('Failed to initialize cutting system after multiple retries');
                    frappe.msgprint(__('Failed to initialize cutting interface. Please try again.'));
                }
            };
            setTimeout(retryInit, 300);
        }
    }, 200);
}

// ============================================================================
// SEQUENTIAL CUTTING SYSTEM
// ============================================================================

// Global state for cutting system
let current_pieces = [];
let selected_piece_index = -1;

function initialize_cutting_system(frm, validation_info) {
    /**
     * Initialize the sequential cutting system
     */
    console.log('Initializing cutting system with validation info:', validation_info);

    const source_dims = validation_info.source_dimensions;
    const material_form = source_dims.material_form;

    console.log('Source dimensions:', source_dims);
    console.log('Material form:', material_form);

    // Initialize with source material as the first piece
    current_pieces = [{
        id: 0,
        dimensions: material_form === 'Block' ?
            { x: source_dims.x, y: source_dims.y, z: source_dims.z } :
            { d: source_dims.d, l: source_dims.l },
        material_form: material_form
    }];

    console.log('Initial pieces:', current_pieces);

    // If there are existing items in the form, load them instead
    if (frm.doc.items && frm.doc.items.length > 0) {
        console.log('Loading existing pieces from form:', frm.doc.items);
        load_existing_pieces(frm.doc.items, material_form);
    }

    // Render the pieces
    console.log('About to render pieces...');
    render_current_pieces();

    // Setup cut dimension options
    setup_cut_dimension_options(material_form);

    console.log('Cutting system initialization complete');
}

function load_existing_pieces(items, material_form) {
    /**
     * Load existing pieces from the form
     */
    current_pieces = items.map((item, index) => ({
        id: index,
        dimensions: material_form === 'Block' ?
            { x: parseFloat(item.x_dim) || 0, y: parseFloat(item.y_dim) || 0, z: parseFloat(item.z_dim) || 0 } :
            { d: parseFloat(item.d_dim) || 0, l: parseFloat(item.l_dim) || 0 },
        material_form: material_form
    }));
}

function render_current_pieces() {
    /**
     * Render the current pieces list
     */
    const pieces_list = document.getElementById('current-pieces-list');
    if (!pieces_list) {
        console.error('Current pieces list not found - DOM may not be ready yet');
        console.log('Available elements:', document.querySelectorAll('[id*="pieces"]'));
        return;
    }

    console.log('Rendering', current_pieces.length, 'pieces');
    pieces_list.innerHTML = '';

    current_pieces.forEach((piece, index) => {
        const piece_html = create_piece_html(piece, index);
        pieces_list.insertAdjacentHTML('beforeend', piece_html);
    });

    // Update pieces count
    const pieces_count = document.getElementById('pieces-count');
    if (pieces_count) {
        pieces_count.textContent = `${current_pieces.length} piece${current_pieces.length !== 1 ? 's' : ''}`;
        pieces_count.className = current_pieces.length === 1 ? 'badge badge-info' : 'badge badge-success';
    }

    // Show/hide help text
    const pieces_help = document.getElementById('pieces-help');
    if (pieces_help) {
        pieces_help.style.display = current_pieces.length === 1 ? 'block' : 'none';
    }

    // Bind click events for piece selection
    bind_piece_selection_events();
}

function create_piece_html(piece, index) {
    /**
     * Create HTML for a single piece
     */
    const dimensions_text = piece.material_form === 'Block' ?
        `${piece.dimensions.x} × ${piece.dimensions.y} × ${piece.dimensions.z} mm` :
        `⌀${piece.dimensions.d} × ${piece.dimensions.l} mm`;

    const is_selected = index === selected_piece_index;

    // Calculate volume/area for display
    const volume_area = piece.material_form === 'Block' ?
        (piece.dimensions.x * piece.dimensions.y * piece.dimensions.z).toFixed(1) + ' mm³' :
        (Math.PI * Math.pow(piece.dimensions.d / 2, 2) * piece.dimensions.l).toFixed(1) + ' mm³';

    return `
        <div class="piece-item ${is_selected ? 'selected' : ''}" data-piece-index="${index}">
            <div class="piece-content">
                <div class="piece-header">
                    <div class="piece-number">
                        <i class="fa fa-cube text-primary"></i>
                        <span class="font-weight-bold">Piece ${index + 1}</span>
                    </div>
                    <div class="piece-status">
                        ${is_selected ?
            '<span class="badge badge-primary"><i class="fa fa-check"></i> Selected</span>' :
            '<span class="badge badge-light">Click to select</span>'
        }
                    </div>
                </div>
                <div class="piece-details">
                    <div class="dimension-display">
                        <i class="fa fa-ruler text-muted"></i>
                        <span class="font-weight-bold text-primary">${dimensions_text}</span>
                    </div>
                    <div class="volume-display">
                        <small class="text-muted">
                            <i class="fa fa-calculator"></i> Volume: ${volume_area}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function bind_piece_selection_events() {
    /**
     * Bind click events for piece selection
     */
    // Bind piece selection events
    document.querySelectorAll('.piece-item').forEach(piece => {
        piece.addEventListener('click', function () {
            const piece_index = parseInt(this.getAttribute('data-piece-index'));
            select_piece_for_cutting(piece_index);
        });
    });
}

function select_piece_for_cutting(piece_index) {
    /**
     * Select a piece for cutting
     */
    selected_piece_index = piece_index;
    const selected_piece = current_pieces[piece_index];

    // Re-render pieces to show selection
    render_current_pieces();

    // Show cutting controls
    show_cutting_controls(selected_piece);
}

function show_cutting_controls(piece) {
    /**
     * Show the cutting controls for the selected piece
     */
    const cutting_controls = document.getElementById('cutting-controls');
    const selected_piece_info = document.getElementById('selected-piece-info');

    if (!cutting_controls || !selected_piece_info) return;

    const dimensions_text = piece.material_form === 'Block' ?
        `${piece.dimensions.x} × ${piece.dimensions.y} × ${piece.dimensions.z}` :
        `${piece.dimensions.d} × ${piece.dimensions.l}`;

    selected_piece_info.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fa fa-cube text-primary mr-2"></i>
            <div>
                <strong>Selected Piece:</strong> ${dimensions_text}<br>
                <small class="text-muted">Choose a dimension and position to cut this piece</small>
            </div>
        </div>
    `;

    cutting_controls.style.display = 'block';

    // Update cut position input max values
    update_cut_position_limits(piece);
}

function setup_cut_dimension_options(material_form) {
    /**
     * Setup the cut dimension dropdown options
     */
    const cut_dimension_select = document.getElementById('cut-dimension');
    if (!cut_dimension_select) return;

    cut_dimension_select.innerHTML = '';

    if (material_form === 'Block') {
        cut_dimension_select.innerHTML = `
            <option value="x">X Dimension</option>
            <option value="y">Y Dimension</option>
            <option value="z">Z Dimension</option>
        `;
    } else {
        cut_dimension_select.innerHTML = `
            <option value="d">D Dimension</option>
            <option value="l">L Dimension</option>
        `;
    }
}

function update_cut_position_limits(piece) {
    /**
     * Update the cut position input limits based on selected piece
     */
    const cut_position_input = document.getElementById('cut-position');
    const cut_dimension_select = document.getElementById('cut-dimension');

    if (!cut_position_input || !cut_dimension_select) return;

    cut_dimension_select.addEventListener('change', function () {
        const selected_dimension = this.value;
        const max_value = piece.dimensions[selected_dimension];

        cut_position_input.max = max_value;
        cut_position_input.placeholder = `0.1 - ${max_value}`;
        cut_position_input.value = '';
    });

    // Trigger initial setup
    cut_dimension_select.dispatchEvent(new Event('change'));
}

// Destination functionality removed - not needed for sequential cutting

function execute_cut() {
    /**
     * Execute the cut on the selected piece
     */
    if (selected_piece_index === -1) {
        frappe.msgprint(__('Please select a piece to cut.'));
        return;
    }

    const cut_dimension = document.getElementById('cut-dimension').value;
    const cut_position = parseFloat(document.getElementById('cut-position').value);

    if (!cut_position || cut_position <= 0) {
        frappe.msgprint(__('Please enter a valid cut position.'));
        return;
    }

    const selected_piece = current_pieces[selected_piece_index];
    const max_dimension = selected_piece.dimensions[cut_dimension];

    if (cut_position >= max_dimension) {
        frappe.msgprint(__(`Cut position (${cut_position}) must be less than the dimension size (${max_dimension}).`));
        return;
    }

    // Perform the cut
    perform_cut(selected_piece_index, cut_dimension, cut_position);
}

function perform_cut(piece_index, cut_dimension, cut_position) {
    /**
     * Perform the actual cut operation
     */
    const original_piece = current_pieces[piece_index];

    // Create two new pieces from the cut
    const piece1 = JSON.parse(JSON.stringify(original_piece)); // Deep copy
    const piece2 = JSON.parse(JSON.stringify(original_piece)); // Deep copy

    // Update dimensions for the two pieces
    piece1.dimensions[cut_dimension] = cut_position;
    piece2.dimensions[cut_dimension] = original_piece.dimensions[cut_dimension] - cut_position;

    // Assign new IDs
    const next_id = Math.max(...current_pieces.map(p => p.id)) + 1;
    piece1.id = next_id;
    piece2.id = next_id + 1;

    // Remove the original piece and add the two new pieces
    current_pieces.splice(piece_index, 1, piece1, piece2);

    // Reset selection
    selected_piece_index = -1;

    // Hide cutting controls
    cancel_cut();

    // Re-render pieces
    render_current_pieces();

    // Show success message with details
    const piece1_text = original_piece.material_form === 'Block' ?
        `${piece1.dimensions.x}×${piece1.dimensions.y}×${piece1.dimensions.z}` :
        `⌀${piece1.dimensions.d}×${piece1.dimensions.l}`;
    const piece2_text = original_piece.material_form === 'Block' ?
        `${piece2.dimensions.x}×${piece2.dimensions.y}×${piece2.dimensions.z}` :
        `⌀${piece2.dimensions.d}×${piece2.dimensions.l}`;

    frappe.show_alert({
        message: __(`✂️ Cut completed! Created 2 pieces: ${piece1_text} mm and ${piece2_text} mm`),
        indicator: 'green'
    });
}

function cancel_cut() {
    /**
     * Cancel the current cut operation
     */
    selected_piece_index = -1;

    const cutting_controls = document.getElementById('cutting-controls');
    if (cutting_controls) {
        cutting_controls.style.display = 'none';
    }

    // Re-render pieces to remove selection
    render_current_pieces();
}

function update_remaining_material_display() {
    /**
     * Placeholder function for backward compatibility
     * The new system doesn't need this as it uses sequential cutting
     */
    console.log('update_remaining_material_display called - using new sequential cutting system');
}

// Old calculate_remaining_material function removed - not needed for sequential cutting

function apply_cuts_to_form(frm, dialog) {
    /**
     * Apply the cuts defined in the dialog to the main form
     */
    if (!current_pieces || current_pieces.length === 0) {
        frappe.msgprint(__('No pieces to apply. Please create some cuts first.'));
        return;
    }

    // Clear existing items
    frm.clear_table('items');

    // Add current pieces to the form
    current_pieces.forEach((piece, index) => {
        const row = frm.add_child('items');

        // Set base item code
        const base_item_code = get_base_item_code(frm.doc.item_code);
        row.item_code = base_item_code;

        // Set dimensions
        if (piece.material_form === 'Block') {
            row.x_dim = piece.dimensions.x;
            row.y_dim = piece.dimensions.y;
            row.z_dim = piece.dimensions.z;
        } else {
            row.d_dim = piece.dimensions.d;
            row.l_dim = piece.dimensions.l;
        }

        // Set warehouse (all pieces go to the same warehouse as source)
        row.warehouse = frm.doc.warehouse;

        // Set serial number placeholder
        if (frm.doc.serial_no) {
            row.serial_no = `${frm.doc.serial_no}-${index + 1}`;
        }
    });

    // Refresh the child table
    frm.refresh_field('items');

    // Trigger rate calculation
    calculate_child_rates(frm);

    // Close dialog
    dialog.hide();

    // Show success message
    frappe.show_alert({
        message: __(`Applied ${current_pieces.length} cut pieces to the form`),
        indicator: 'green'
    });

    // Clean up global variables
    delete window.cutting_dialog;
    delete window.cutting_validation_info;
    delete window.cutting_frm;

    // Clean up cutting state
    current_pieces = [];
    selected_piece_index = -1;
}

// Make functions globally accessible for debugging and inline handlers
window.execute_cut = execute_cut;
window.cancel_cut = cancel_cut;
window.update_remaining_material_display = update_remaining_material_display;
