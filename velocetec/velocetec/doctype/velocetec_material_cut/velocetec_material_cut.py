# Copyright (c) 2025, Sydney Kibang<PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class VelocetecMaterialCut(Document):
    def validate(self):
        """Validate document before saving"""
        self.validate_dimensions()
        self.calculate_scrap_material()
        self.calculate_child_rates()

    def on_submit(self):
        """Create dimensioned items, create child serial numbers, then create stock entry"""
        # Final validation before submission
        self.validate_dimensions()

        self.create_dimensioned_items()
        self.create_child_serial_numbers()
        self.create_repack_stock_entry()

        # Create scrap entry if needed
        self.create_scrap_entry_if_needed()

    def create_dimensioned_items(self):
        """Create items with dimensions and update child table references"""
        # Get the base item code (remove existing dimension suffix if present)
        base_item_code = self._get_base_item_code(self.item_code)

        for item in self.items:
            # Generate dimensions string
            dimensions = self._get_dimensions_string(item)
            if not dimensions:
                frappe.throw(f"Dimensions missing or invalid for item {item.item_code}")

            # Create dimensioned item code using base item code
            dimensioned_item_code = f"{base_item_code} - {dimensions}"

            # Create Material Size if it doesn't exist
            self._create_material_size_if_needed(item, dimensions)

            # Create dimensioned Item if it doesn't exist
            if not frappe.db.exists("Item", dimensioned_item_code):
                self._create_dimensioned_item(
                    dimensioned_item_code, dimensions, base_item_code
                )
                frappe.msgprint(
                    f"Item {dimensioned_item_code} created with serial numbers enabled."
                )
            else:
                # Ensure existing dimensioned item has serial numbers enabled
                existing_item = frappe.get_doc("Item", dimensioned_item_code)
                if not existing_item.has_serial_no:
                    existing_item.has_serial_no = 1
                    existing_item.serial_no_series = frappe.get_value(
                        "Item", self.item_code, "serial_no_series"
                    )
                    existing_item.save(ignore_permissions=True)
                    frappe.msgprint(
                        f"Item {dimensioned_item_code} updated to enable serial numbers."
                    )

            # Update the child table item_code to use the dimensioned item
            item.db_set(
                "dimensioned_item_code", dimensioned_item_code, update_modified=False
            )

    def create_child_serial_numbers(self):
        """Create child serial numbers based on parent serial number and mark parent as consumed"""
        if not self.serial_no:
            frappe.throw("Parent Serial No is required to create child serial numbers")

        # Get the base item code and validate that it has serial numbers enabled
        base_item_code = self._get_base_item_code(self.item_code)
        parent_item = frappe.get_doc("Item", base_item_code)
        if not parent_item.has_serial_no:
            frappe.throw(
                f"Base item {base_item_code} must have 'Has Serial No' enabled to create child serial numbers"
            )

        # Get parent serial number details
        parent_serial_doc = frappe.get_doc("Serial No", self.serial_no)

        # Create child serial numbers for each item
        for idx, item in enumerate(self.items, 1):
            # Generate child serial number
            child_serial_no = f"{self.serial_no}-{idx}"

            # Check if serial number already exists
            if frappe.db.exists("Serial No", child_serial_no):
                # If it exists, skip creating it but still update the child table
                frappe.msgprint(
                    f"Serial Number {child_serial_no} already exists, skipping creation"
                )
                item.db_set("serial_no", child_serial_no, update_modified=False)
                continue

            # Get the dimensioned item code or fallback to original item_code
            target_item_code = (
                getattr(item, "dimensioned_item_code", None) or item.item_code
            )

            # Create child serial number document
            child_serial_doc = frappe.get_doc(
                {
                    "doctype": "Serial No",
                    "serial_no": child_serial_no,
                    "item_code": target_item_code,
                    "status": "Inactive",  # Start as Inactive, will be activated by Stock Entry
                    "company": parent_serial_doc.company,
                    "purchase_rate": item.basic_rate or parent_serial_doc.purchase_rate,
                }
            )

            # Insert the child serial number
            child_serial_doc.insert(ignore_permissions=True)

            # Update the child table with the created serial number
            item.db_set("serial_no", child_serial_no, update_modified=False)

            frappe.msgprint(
                f"Serial Number {child_serial_no} created for item {target_item_code}"
            )

        # Mark parent serial number as consumed (will be done by Stock Entry)
        # We don't mark it as consumed here because the Stock Entry needs to process it first

    def create_repack_stock_entry(self):
        """Create a repack stock entry for material cutting"""
        stock_entry = frappe.get_doc(
            {"doctype": "Stock Entry", "stock_entry_type": "Repack", "items": []}
        )

        # Add source item (outgoing)
        stock_entry.append(
            "items",
            {
                "item_code": self.item_code,
                "qty": 1,
                "s_warehouse": self.warehouse,
                "use_serial_batch_fields": 1,
                "serial_no": self.serial_no,
            },
        )

        # Add target items (incoming) - use dimensioned item codes
        for item in self.items:
            # Use the dimensioned item code instead of original item_code
            target_item_code = (
                getattr(item, "dimensioned_item_code", None) or item.item_code
            )

            # Check if the target item has batch tracking enabled
            target_item_doc = frappe.get_doc("Item", target_item_code)

            # Prepare stock entry item data
            stock_entry_item = {
                "item_code": target_item_code,
                "qty": 1,
                "t_warehouse": item.warehouse,
                "use_serial_batch_fields": 1,
                "serial_no": item.serial_no,
                "basic_rate": item.basic_rate,
                "set_basic_rate_manually": 1,
            }

            # Add batch number if the item has batch tracking
            if target_item_doc.has_batch_no:
                # Find the batch for this dimensioned item
                batch_name = frappe.db.get_value(
                    "Batch", {"item": target_item_code}, "name"
                )
                if batch_name:
                    stock_entry_item["batch_no"] = batch_name
                else:
                    frappe.throw(
                        f"Batch not found for item {target_item_code}. Cannot proceed with Stock Entry."
                    )

            stock_entry.append("items", stock_entry_item)

        stock_entry.insert()
        stock_entry.save()
        stock_entry.submit()

        # Update the material cut with the stock entry reference
        frappe.db.set_value(
            self.doctype, self.name, "repack_stock_entry", stock_entry.name
        )

        # Show success message
        stock_entry_link = frappe.utils.get_link_to_form(
            "Stock Entry", stock_entry.name
        )
        frappe.msgprint(f"Stock Entry {stock_entry_link} created successfully.")

    # Old destination-related methods removed - not needed for sequential cutting

    # Old destination and scrap-related methods removed - not needed for sequential cutting

    def _get_dimensions_string(self, item):
        """Generate dimensions string from item dimensions"""
        if item.x_dim and item.y_dim and item.z_dim and not (item.d_dim or item.l_dim):
            return f"{item.x_dim}x{item.y_dim}x{item.z_dim}"
        elif item.d_dim and item.l_dim and not (item.x_dim or item.y_dim or item.z_dim):
            return f"{item.d_dim}x{item.l_dim}"
        return None

    def _create_material_size_if_needed(self, item, dimensions):
        """Create Material Size if it doesn't exist"""
        if not frappe.db.exists("Velocetec Material Size", {"name": dimensions}):
            if item.x_dim and item.y_dim and item.z_dim:
                material_form = "Block"
                dimension_dict = {
                    "x_dim": item.x_dim,
                    "y_dim": item.y_dim,
                    "z_dim": item.z_dim,
                }
            elif item.d_dim and item.l_dim:
                material_form = "Bar"
                dimension_dict = {"d_dim": item.d_dim, "l_dim": item.l_dim}
            else:
                frappe.throw(f"Invalid dimensions for item {item.item_code}")

            new_size = frappe.get_doc(
                {
                    "doctype": "Velocetec Material Size",
                    "material_form": material_form,
                    **dimension_dict,
                }
            )
            new_size.insert(ignore_permissions=True)

    def _create_dimensioned_item(
        self, dimensioned_item_code, dimensions, base_item_code=None
    ):
        """Create a new item with dimensions"""

        # Use provided base_item_code or fall back to self.item_code
        source_item_code = base_item_code or self.item_code
        base_item = frappe.get_doc("Item", source_item_code)

        new_item = frappe.get_doc(
            {
                "doctype": "Item",
                "item_code": dimensioned_item_code,
                "item_name": dimensioned_item_code,
                "item_group": base_item.item_group or "Custom Manufactured Parts",
                "stock_uom": base_item.stock_uom or "Nos",
                "is_sales_item": 1,
                "is_stock_item": 1,
                "has_serial_no": 1,  # Enable serial numbers for dimensioned items
                "has_batch_no": base_item.has_batch_no or 1,  # Enable batch tracking
                "serial_no_series": base_item.serial_no_series,  # Copy serial number series from parent
                "batch_number_series": base_item.batch_number_series,  # Copy batch series from parent
                "material_size": dimensions,
                "taxes": base_item.taxes,
            }
        )
        new_item.insert(ignore_permissions=True)

        # Create batch for the dimensioned item if batch tracking is enabled
        if new_item.has_batch_no:
            from velocetec.utils.batch_utils import create_batch_for_dimensioned_item

            batch_id = create_batch_for_dimensioned_item(
                item_code=dimensioned_item_code,
                base_item_code=source_item_code,
                serial_no_series=base_item.serial_no_series,
            )

            if not batch_id:
                frappe.log_error(
                    message=f"Failed to create batch for dimensioned item {dimensioned_item_code} via Material Cut",
                    title="Material Cut Batch Creation Failed",
                )

    def _get_base_item_code(self, item_code):
        """Extract base item code by removing dimension suffix if present"""
        import re

        # Pattern to match dimension suffixes like " - 10x20x21", " - 15x100", or " - 234.0x324.0x234.0"
        # This matches " - " followed by decimal numbers and 'x' characters
        dimension_pattern = r"\s*-\s*[\d.]+x[\d.]+(?:x[\d.]+)?$"

        # Remove the dimension suffix if it exists
        base_item_code = re.sub(dimension_pattern, "", item_code)

        # Validate that the base item exists
        if not frappe.db.exists("Item", base_item_code):
            # If base item doesn't exist, try to find it by checking if current item has material_size
            current_item = frappe.get_doc("Item", item_code)
            if current_item.material_size:
                # If current item has material_size, it's already a dimensioned item
                # Try to find the base item by removing the material size from the name
                potential_base = item_code.replace(
                    f" - {current_item.material_size}", ""
                )
                if frappe.db.exists("Item", potential_base):
                    return potential_base

            # If we can't find a base item, use the original item_code
            frappe.msgprint(
                f"Warning: Could not find base item for {item_code}. Using original item code."
            )
            return item_code

        return base_item_code

    def validate_dimensions(self):
        """Validate that child dimensions don't exceed source material dimensions"""
        if not self.material_size or not self.items:
            return

        # Get source material dimensions
        source_dimensions = self._get_source_material_dimensions()
        if not source_dimensions:
            return

        # Get material form
        material_form = source_dimensions.get("material_form")

        # No validation needed for sequential cutting - pieces are created progressively

    def _get_source_material_dimensions(self):
        """Get source material dimensions from material size"""
        if not self.material_size:
            return None

        try:
            material_size_doc = frappe.get_doc(
                "Velocetec Material Size", self.material_size
            )

            dimensions = {"material_form": material_size_doc.material_form}

            if material_size_doc.material_form == "Block":
                dimensions.update(
                    {
                        "x": float(material_size_doc.x_dim or 0),
                        "y": float(material_size_doc.y_dim or 0),
                        "z": float(material_size_doc.z_dim or 0),
                    }
                )
            elif material_size_doc.material_form == "Bar":
                dimensions.update(
                    {
                        "d": float(material_size_doc.d_dim or 0),
                        "l": float(material_size_doc.l_dim or 0),
                    }
                )

            return dimensions

        except Exception as e:
            frappe.log_error(f"Error getting source material dimensions: {str(e)}")
            return None

    # Old validation method removed - not needed for sequential cutting

    def calculate_scrap_material(self):
        """Calculate remaining material (scrap) after cutting operations"""
        if not hasattr(self, "_validation_result") or not self._validation_result:
            return

        validation_result = self._validation_result
        material_form = validation_result["material_form"]

        # Calculate remaining material
        remaining_material = {}
        has_scrap = False

        if material_form == "Block":
            remaining_material = {
                "x": validation_result["source_dimensions"]["x"]
                - validation_result["total_used"]["x"],
                "y": validation_result["source_dimensions"]["y"]
                - validation_result["total_used"]["y"],
                "z": validation_result["source_dimensions"]["z"]
                - validation_result["total_used"]["z"],
            }
            has_scrap = (
                remaining_material["x"] > 0
                or remaining_material["y"] > 0
                or remaining_material["z"] > 0
            )

        elif material_form == "Bar":
            remaining_material = {
                "d": validation_result["source_dimensions"]["d"]
                - validation_result["total_used"]["d"],
                "l": validation_result["source_dimensions"]["l"]
                - validation_result["total_used"]["l"],
            }
            has_scrap = remaining_material["d"] > 0 or remaining_material["l"] > 0

        # Store scrap information for later use
        if has_scrap and not validation_result["errors"]:
            self._scrap_material = {
                "remaining_dimensions": remaining_material,
                "material_form": material_form,
                "has_scrap": True,
            }
        else:
            self._scrap_material = {"has_scrap": False}

    @frappe.whitelist()
    def calculate_child_rates(self):
        """Calculate base rates for child items based on dimensional splitting"""
        if not self.material_size or not self.items or not self.basic_rate:
            return

        # Get source material dimensions
        source_dimensions = self._get_source_material_dimensions()
        if not source_dimensions:
            return

        material_form = source_dimensions.get("material_form")

        # Calculate source material volume/workings
        source_volume = self._calculate_material_volume(
            source_dimensions, material_form
        )
        if source_volume <= 0:
            return

        # Calculate base unit equivalent from source material
        # Base Unit Equivalent = Source Material Workings / Source Material Rate per KG
        base_unit_equivalent = source_volume / float(self.basic_rate)

        # Calculate rate for each child item
        for item in self.items:
            child_volume = self._calculate_item_volume(item, material_form)
            if child_volume > 0:
                # Cut Material Rate per KG = Cut Material Workings / Base Unit Equivalent
                item.basic_rate = child_volume / base_unit_equivalent
            else:
                item.basic_rate = 0

    def _calculate_material_volume(self, dimensions, material_form):
        """Calculate volume/workings for material dimensions"""
        try:
            if material_form == "Block":
                x = float(dimensions.get("x", 0))
                y = float(dimensions.get("y", 0))
                z = float(dimensions.get("z", 0))
                return x * y * z
            elif material_form == "Bar":
                d = float(dimensions.get("d", 0))
                l = float(dimensions.get("l", 0))
                # For bars, we use diameter * length as volume equivalent
                return d * l
            return 0
        except (ValueError, TypeError):
            return 0

    def _calculate_item_volume(self, item, material_form):
        """Calculate volume/workings for a child item"""
        try:
            if material_form == "Block":
                x = float(item.x_dim or 0)
                y = float(item.y_dim or 0)
                z = float(item.z_dim or 0)
                return x * y * z
            elif material_form == "Bar":
                d = float(item.d_dim or 0)
                l = float(item.l_dim or 0)
                return d * l
            return 0
        except (ValueError, TypeError):
            return 0

    def create_scrap_entry_if_needed(self):
        """Create scrap entry if there's remaining material"""
        if not hasattr(self, "_scrap_material") or not self._scrap_material.get(
            "has_scrap"
        ):
            return

        scrap_info = self._scrap_material

        try:
            # Generate scrap dimensions string
            scrap_dimensions = self._generate_scrap_dimensions_string(
                scrap_info["remaining_dimensions"], scrap_info["material_form"]
            )

            if not scrap_dimensions:
                return

            # Get base item code
            base_item_code = self._get_base_item_code(self.item_code)

            # Create scrap item code
            scrap_item_code = f"{base_item_code} - {scrap_dimensions} - SCRAP"

            # Create scrap item if it doesn't exist
            self._create_or_get_scrap_item(
                scrap_item_code,
                scrap_dimensions,
                base_item_code,
                scrap_info["material_form"],
                scrap_info["remaining_dimensions"],
            )

            # Create scrap serial number
            scrap_serial_no = self._create_scrap_serial_number(
                self.serial_no, scrap_item_code
            )

            # Create stock entry for scrap
            stock_entry = self._create_scrap_stock_entry(
                scrap_item_code, scrap_serial_no
            )

            # Store the scrap stock entry reference
            frappe.db.set_value(
                self.doctype, self.name, "scrap_stock_entry", stock_entry.name
            )

            # Show success message
            frappe.msgprint(
                f"Scrap material created: {scrap_item_code} with Stock Entry {stock_entry.name}",
                title="Scrap Entry Created",
                indicator="green",
            )

        except Exception as e:
            frappe.log_error(f"Error creating scrap entry: {str(e)}")
            frappe.msgprint(
                f"Warning: Could not create scrap entry - {str(e)}",
                title="Scrap Creation Warning",
                indicator="orange",
            )

    def _generate_scrap_dimensions_string(self, remaining_dimensions, material_form):
        """Generate dimensions string for scrap material"""
        try:
            if material_form == "Block":
                x = float(remaining_dimensions.get("x", 0))
                y = float(remaining_dimensions.get("y", 0))
                z = float(remaining_dimensions.get("z", 0))

                if x > 0 and y > 0 and z > 0:
                    return f"{x}x{y}x{z}"

            elif material_form == "Bar":
                d = float(remaining_dimensions.get("d", 0))
                l = float(remaining_dimensions.get("l", 0))

                if d > 0 and l > 0:
                    return f"{d}x{l}"

            return None
        except (ValueError, TypeError):
            return None

    def _create_or_get_scrap_item(
        self,
        scrap_item_code,
        scrap_dimensions,
        base_item_code,
        material_form,
        remaining_dimensions,
    ):
        """Create or get existing scrap item"""

        # Check if scrap item already exists
        if frappe.db.exists("Item", scrap_item_code):
            return frappe.get_doc("Item", scrap_item_code)

        # Get base item for reference
        base_item = frappe.get_doc("Item", base_item_code)

        # Create material size for scrap if needed
        self._create_scrap_material_size(
            scrap_dimensions, material_form, remaining_dimensions
        )

        # Create scrap item (same format as regular dimensioned items)
        scrap_item = frappe.get_doc(
            {
                "doctype": "Item",
                "item_code": scrap_item_code,
                "item_name": scrap_item_code,
                "item_group": base_item.item_group or "Custom Manufactured Parts",
                "stock_uom": base_item.stock_uom or "Nos",
                "is_sales_item": 1,
                "is_stock_item": 1,
                "has_serial_no": 1,
                "serial_no_series": base_item.serial_no_series,
                "material_size": scrap_dimensions,
                "taxes": base_item.taxes,
            }
        )

        scrap_item.insert(ignore_permissions=True)
        return scrap_item

    def _create_scrap_material_size(
        self, scrap_dimensions, material_form, remaining_dimensions
    ):
        """Create material size for scrap if it doesn't exist"""

        if frappe.db.exists("Velocetec Material Size", scrap_dimensions):
            return

        material_size_doc = {
            "doctype": "Velocetec Material Size",
            "material_form": material_form,
        }

        if material_form == "Block":
            material_size_doc.update(
                {
                    "x_dim": remaining_dimensions.get("x"),
                    "y_dim": remaining_dimensions.get("y"),
                    "z_dim": remaining_dimensions.get("z"),
                }
            )
        elif material_form == "Bar":
            material_size_doc.update(
                {
                    "d_dim": remaining_dimensions.get("d"),
                    "l_dim": remaining_dimensions.get("l"),
                }
            )

        material_size = frappe.get_doc(material_size_doc)
        material_size.insert(ignore_permissions=True)

    def _create_scrap_serial_number(self, parent_serial_no, scrap_item_code):
        """Create serial number for scrap material (same pattern as regular repack)"""

        # Find the next available serial number using the same pattern as regular items
        # Get the highest existing serial number for this parent
        existing_serials = frappe.db.sql(
            """
            SELECT serial_no FROM `tabSerial No`
            WHERE serial_no LIKE %s
            ORDER BY serial_no DESC
        """,
            (f"{parent_serial_no}-%",),
        )

        # Determine the next index
        next_index = len(self.items) + 1  # Start after the regular items
        if existing_serials:
            # Find the highest index and increment
            for serial_tuple in existing_serials:
                serial = serial_tuple[0]
                if serial.startswith(f"{parent_serial_no}-"):
                    try:
                        index = int(serial.split("-")[-1])
                        if index >= next_index:
                            next_index = index + 1
                    except (ValueError, IndexError):
                        continue

        scrap_serial_no = f"{parent_serial_no}-{next_index}"

        # Ensure it doesn't exist (safety check)
        while frappe.db.exists("Serial No", scrap_serial_no):
            next_index += 1
            scrap_serial_no = f"{parent_serial_no}-{next_index}"

        # Get parent serial details
        parent_serial = frappe.get_doc("Serial No", parent_serial_no)

        # Create scrap serial number
        scrap_serial = frappe.get_doc(
            {
                "doctype": "Serial No",
                "serial_no": scrap_serial_no,
                "item_code": scrap_item_code,
                "status": "Inactive",
                "company": parent_serial.company,
                "purchase_rate": 0,  # Scrap has no purchase value
            }
        )

        scrap_serial.insert(ignore_permissions=True)
        return scrap_serial_no

    def _create_scrap_stock_entry(self, scrap_item_code, scrap_serial_no):
        """Create stock entry to add scrap to inventory"""

        stock_entry = frappe.get_doc(
            {
                "doctype": "Stock Entry",
                "stock_entry_type": "Material Receipt",
                "purpose": "Material Receipt",
                "company": self.get("company")
                or frappe.defaults.get_user_default("Company"),
                "items": [
                    {
                        "item_code": scrap_item_code,
                        "qty": 1,
                        "t_warehouse": self.warehouse,
                        "use_serial_batch_fields": 1,
                        "serial_no": scrap_serial_no,
                        "basic_rate": 0,  # Scrap has no value
                        "set_basic_rate_manually": 1,
                    }
                ],
            }
        )

        stock_entry.insert(ignore_permissions=True)
        stock_entry.save()
        stock_entry.submit()

        return stock_entry

    # Old validation API method removed - not needed for sequential cutting

    @frappe.whitelist()
    def create_scrap_entry(self, remaining_dimensions, material_form):
        """
        Create a scrap entry for remaining material after material cutting

        Args:
            remaining_dimensions: Dict or JSON string of remaining dimensions
            material_form: "Block" or "Bar"

        Returns:
            dict: Success status and created item/stock entry details
        """
        import json

        try:
            # Parse remaining dimensions if it's a string
            if isinstance(remaining_dimensions, str):
                remaining_dimensions = json.loads(remaining_dimensions)

            # Validate that material cut is in draft state
            if self.docstatus != 0:
                return {
                    "success": False,
                    "error": "Cannot create scrap for submitted Material Cut",
                }

            # Generate scrap dimensions string
            scrap_dimensions = self._generate_scrap_dimensions_string(
                remaining_dimensions, material_form
            )

            if not scrap_dimensions:
                return {"success": False, "error": "Invalid remaining dimensions"}

            # Get base item code
            base_item_code = self._get_base_item_code(self.item_code)

            # Create scrap item code (same format as regular items, no SCRAP suffix)
            scrap_item_code = f"{base_item_code} - {scrap_dimensions}"

            # Create scrap item if it doesn't exist
            scrap_item = self._create_or_get_scrap_item(
                scrap_item_code,
                scrap_dimensions,
                base_item_code,
                material_form,
                remaining_dimensions,
            )

            # Create scrap serial number
            scrap_serial_no = self._create_scrap_serial_number(
                self.serial_no, scrap_item_code
            )

            # Create stock entry for scrap
            stock_entry = self._create_scrap_stock_entry(
                scrap_item_code, scrap_serial_no
            )

            # Store the scrap stock entry reference in the document
            # Note: You may need to add a custom field 'scrap_stock_entry' to the doctype
            try:
                frappe.db.set_value(
                    self.doctype, self.name, "scrap_stock_entry", stock_entry.name
                )
            except Exception:
                # Field might not exist yet, log but don't fail
                frappe.log_error(
                    "Could not save scrap_stock_entry field - field may not exist"
                )

            return {
                "success": True,
                "scrap_item_code": scrap_item_code,
                "scrap_serial_no": scrap_serial_no,
                "stock_entry": stock_entry.name,
                "message": "Scrap entry created successfully",
            }

        except Exception as e:
            frappe.log_error(f"Error creating scrap entry: {str(e)}")
            return {"success": False, "error": f"Error creating scrap entry: {str(e)}"}
