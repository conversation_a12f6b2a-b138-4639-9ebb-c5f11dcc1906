{"actions": [], "allow_rename": 1, "creation": "2025-04-15 15:21:30.685771", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["routing_details", "column_break_dzko", "duplicate", "column_break_axu0", "is_child_part", "parent_part", "parent_id", "section_break_oiym", "column_break_mxlu", "part_number", "dup_reference_part_number", "dup_reference_part_number_name", "is_dup_reference_created", "column_break_uvqx", "description", "batch_cost", "each_cost", "column_break_cuq0", "quantity", "mark_up_item", "column_break_zxy3", "delivery_date", "lead_time", "lead_time_days", "sell_price_each", "sell_cost_each", "own_sell_cost_each", "add_to_parts_sell_cost_each", "section_break_vist", "material_price", "machining_section", "machining", "inspection", "design", "column_break_bvdj", "mass_finishing", "finishing", "edm", "column_break_ynhj", "other", "sub_con", "assembly", "column_break_joyj", "tool_making", "turning", "fixings_cost_section", "fixings", "section_break_ytji", "delivery_type", "column_break_bhc3", "delivery_cost", "column_break_pjdt", "no_of_shipments", "column_break_fgcd", "delivery_total", "column_break_rnwu", "delivery_each", "notes_section", "internal_notes", "column_break_mduz", "external_notes", "section_break_zcwj", "line_total", "velocetec_line_costing", "column_break_rqgc", "reference_doctype", "reference_docname"], "fields": [{"fieldname": "routing_details", "fieldtype": "<PERSON><PERSON>", "in_list_view": 1, "label": "Line Costing"}, {"fieldname": "column_break_dzko", "fieldtype": "Column Break"}, {"fieldname": "duplicate", "fieldtype": "<PERSON><PERSON>", "label": "Duplicate Line"}, {"fieldname": "column_break_axu0", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_child_part", "fieldtype": "Check", "label": "Is Child Part"}, {"depends_on": "is_child_part", "fieldname": "parent_part", "fieldtype": "Select", "label": "Parent Part", "mandatory_depends_on": "is_child_part", "search_index": 1}, {"fieldname": "parent_id", "fieldtype": "Data", "hidden": 1, "label": "Parent ID", "read_only": 1, "search_index": 1}, {"fieldname": "section_break_oiym", "fieldtype": "Section Break", "label": "<PERSON><PERSON>"}, {"fieldname": "column_break_mxlu", "fieldtype": "Column Break", "hidden": 1, "read_only": 1}, {"fieldname": "part_number", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Part Number", "search_index": 1}, {"fieldname": "dup_reference_part_number", "fieldtype": "Data", "label": "Dup Reference Part Number", "read_only": 1, "search_index": 1}, {"fieldname": "dup_reference_part_number_name", "fieldtype": "Data", "label": "Dup Reference Part Number Name", "read_only": 1, "search_index": 1}, {"default": "0", "fieldname": "is_dup_reference_created", "fieldtype": "Check", "hidden": 1, "label": "Is Dup Reference Created"}, {"fieldname": "column_break_uvqx", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Data", "in_list_view": 1, "label": "Description "}, {"fieldname": "batch_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> Cost", "read_only": 1}, {"fieldname": "each_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Each Cost", "read_only": 1}, {"fieldname": "column_break_cuq0", "fieldtype": "Column Break"}, {"columns": 1, "default": "1", "fieldname": "quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity "}, {"columns": 1, "default": "0", "fieldname": "mark_up_item", "fieldtype": "Percent", "in_list_view": 1, "label": "Mark Up %"}, {"fieldname": "column_break_zxy3", "fieldtype": "Column Break"}, {"columns": 1, "fieldname": "delivery_date", "fieldtype": "Date", "in_list_view": 1, "label": "Delivery Date", "mandatory_depends_on": "eval: !(doc.lead_time_days)"}, {"fieldname": "lead_time", "fieldtype": "Duration", "label": "Lead Time", "hidden": 1}, {"fieldname": "lead_time_days", "fieldtype": "Int", "label": "Lead Time in Days", "mandatory_depends_on": "eval: !(doc.delivery_date)"}, {"columns": 1, "fieldname": "sell_price_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Sell Price Each", "read_only": 1}, {"fieldname": "sell_cost_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "<PERSON><PERSON> Cost Each", "read_only": 1}, {"fieldname": "own_sell_cost_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Own Sell Cost Each", "read_only": 1}, {"fieldname": "section_break_vist", "fieldtype": "Section Break"}, {"fieldname": "material_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Material Price", "read_only": 1}, {"fieldname": "machining_section", "fieldtype": "Section Break", "label": "Routing & Other Costs"}, {"default": "0", "fieldname": "machining", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Machining", "read_only": 1, "search_index": 1}, {"fieldname": "inspection", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Inspection", "read_only": 1, "search_index": 1}, {"fieldname": "design", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Design", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_bvdj", "fieldtype": "Column Break"}, {"fieldname": "mass_finishing", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Mass Finishing", "read_only": 1, "search_index": 1}, {"fieldname": "finishing", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Finishing", "read_only": 1, "search_index": 1}, {"fieldname": "edm", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "EDM", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_ynhj", "fieldtype": "Column Break"}, {"fieldname": "other", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Other", "read_only": 1, "search_index": 1}, {"fieldname": "sub_con", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Subcontract", "read_only": 1, "search_index": 1}, {"fieldname": "assembly", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Assembly", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_joyj", "fieldtype": "Column Break"}, {"fieldname": "tool_making", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tool Making", "read_only": 1, "search_index": 1}, {"fieldname": "turning", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Turning", "read_only": 1, "search_index": 1}, {"fieldname": "fixings_cost_section", "fieldtype": "Section Break", "label": "Fixings Cost"}, {"fieldname": "fixings", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Fixings", "read_only": 1}, {"fieldname": "section_break_ytji", "fieldtype": "Section Break", "label": "Delivery Details"}, {"fieldname": "delivery_type", "fieldtype": "Link", "label": "Delivery Type", "options": "Shipping Rule"}, {"fieldname": "column_break_bhc3", "fieldtype": "Column Break"}, {"fetch_from": "delivery_type.shipping_amount", "fieldname": "delivery_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delivery Cost", "read_only": 1}, {"fieldname": "column_break_pjdt", "fieldtype": "Column Break"}, {"fieldname": "no_of_shipments", "fieldtype": "Float", "label": "No of Shipments"}, {"fieldname": "column_break_fgcd", "fieldtype": "Column Break"}, {"fieldname": "delivery_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delivery Total", "read_only": 1}, {"fieldname": "column_break_rnwu", "fieldtype": "Column Break"}, {"fieldname": "delivery_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delivery Each", "read_only": 1}, {"fieldname": "section_break_zcwj", "fieldtype": "Section Break", "hidden": 1}, {"fieldname": "line_total", "fieldtype": "Float", "in_list_view": 1, "label": "Line Total", "read_only": 1}, {"fieldname": "velocetec_line_costing", "fieldtype": "Text", "label": "Velocetec Line Costing"}, {"fieldname": "column_break_rqgc", "fieldtype": "Column Break"}, {"fieldname": "reference_doctype", "fieldtype": "Data", "label": "Reference Doctype", "read_only": 1}, {"fieldname": "reference_docname", "fieldtype": "Data", "label": "Reference Docname", "read_only": 1}, {"fieldname": "internal_notes", "fieldtype": "Text", "label": "Internal Notes", "read_only": 1}, {"fieldname": "external_notes", "fieldtype": "Text", "label": "External Notes", "read_only": 1}, {"collapsible": 1, "fieldname": "notes_section", "fieldtype": "Section Break", "label": "Notes"}, {"fieldname": "column_break_mduz", "fieldtype": "Column Break"}, {"fieldname": "add_to_parts_sell_cost_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "Add to parts sell cost each", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-24 11:40:20.015655", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Costing Detail", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "search_fields": "part_number,parent_id,parent_part,is_child_part", "sort_field": "modified", "sort_order": "DESC", "states": []}