{"actions": [], "allow_rename": 1, "creation": "2024-12-18 05:52:28.867462", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "dimensioned_item_code", "warehouse", "serial_no", "basic_rate", "column_break_wfuv", "x_dim", "y_dim", "z_dim", "column_break_vdxj", "d_dim", "l_dim"], "fields": [{"columns": 2, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>"}, {"columns": 2, "fieldname": "dimensioned_item_code", "fieldtype": "Data", "label": "Dimensioned Item Code", "no_copy": 1, "read_only": 1}, {"columns": 2, "fieldname": "warehouse", "fieldtype": "Link", "in_list_view": 1, "label": "Warehouse", "options": "Warehouse", "reqd": 1}, {"columns": 2, "fieldname": "serial_no", "fieldtype": "Data", "in_list_view": 1, "label": "Serial No", "no_copy": 1}, {"fieldname": "basic_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Rate"}, {"fieldname": "column_break_wfuv", "fieldtype": "Column Break"}, {"columns": 1, "fieldname": "x_dim", "fieldtype": "Data", "label": "X"}, {"columns": 1, "fieldname": "y_dim", "fieldtype": "Data", "label": "Y"}, {"columns": 1, "fieldname": "z_dim", "fieldtype": "Data", "label": "Z"}, {"columns": 1, "fieldname": "d_dim", "fieldtype": "Data", "label": "D"}, {"columns": 1, "fieldname": "l_dim", "fieldtype": "Data", "label": "L"}, {"fieldname": "column_break_vdxj", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-24 12:00:00.000000", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Material Cut Item", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}