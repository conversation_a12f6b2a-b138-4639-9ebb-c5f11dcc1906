{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-04-18 03:18:51.209789", "doctype": "DocType", "engine": "InnoDB", "field_order": ["return_to_costing", "posting_date", "column_break_dasw", "velocetec_costing", "column_break_veiw", "velocetec_costing_detail", "section_break_rnab", "part_number", "column_break_oxjw", "description", "column_break_neqx", "inspection_percent", "column_break_vwu6", "minimum_inspection_qty", "section_break_hhil", "line_material_details", "section_break_kdit", "spacer_1", "column_break_2wml", "block_material_amount", "section_break_ampr", "bar_material_details", "section_break_6jr2", "spacer_2", "column_break_8kuq", "bar_material_amount", "velocetec_line_routing_section", "routing_template", "routing_material_details", "velocetec_routing_summary_section", "summary_details", "section_break_8fyy", "qtyload", "column_break_dit9", "no_of_loads", "section_break_bqkf", "html_vfuh", "column_break_oqpn", "total_mass_finishing", "section_break_awcy", "line_fixings", "section_break_la6n", "spacer_4", "column_break_olro", "total_fixings_cost", "section_break_xdkc", "machining", "finishing", "inspection", "other", "edm", "tool_making", "fixings", "column_break_lsyq", "sub_con", "design", "assembly", "turning", "mass_finishing", "material_price", "notes_section", "internal_notes", "column_break_mxqi", "external_notes"], "fields": [{"fieldname": "return_to_costing", "fieldtype": "<PERSON><PERSON>", "label": "Return to Costing"}, {"fetch_from": "velocetec_costing.posting_date", "fieldname": "posting_date", "fieldtype": "Date", "in_filter": 1, "in_list_view": 1, "label": "Posting Date", "read_only": 1}, {"fieldname": "column_break_dasw", "fieldtype": "Column Break"}, {"fieldname": "velocetec_costing", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Velocetec Costing", "options": "Velocetec Costing", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_veiw", "fieldtype": "Column Break"}, {"fieldname": "velocetec_costing_detail", "fieldtype": "Data", "label": "Velocetec Costing Detail", "read_only": 1, "search_index": 1}, {"fieldname": "section_break_rnab", "fieldtype": "Section Break"}, {"fieldname": "part_number", "fieldtype": "Data", "in_filter": 1, "label": "Part Number", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_oxjw", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Data", "label": "Description", "read_only": 1}, {"fieldname": "column_break_neqx", "fieldtype": "Column Break"}, {"default": "100", "fieldname": "inspection_percent", "fieldtype": "Percent", "label": "Inspection %"}, {"fieldname": "column_break_vwu6", "fieldtype": "Column Break"}, {"fieldname": "minimum_inspection_qty", "fieldtype": "Float", "label": "Minimum Inspection Qty"}, {"collapsible": 1, "collapsible_depends_on": "line_material_details", "fieldname": "section_break_hhil", "fieldtype": "Section Break", "label": "Velocetec Line Block Material"}, {"fieldname": "line_material_details", "fieldtype": "Table", "options": "Velocetec Line Block Material Detail", "search_index": 1}, {"depends_on": "line_material_details", "fieldname": "section_break_kdit", "fieldtype": "Section Break"}, {"fieldname": "spacer_1", "fieldtype": "HTML"}, {"fieldname": "column_break_2wml", "fieldtype": "Column Break"}, {"fieldname": "block_material_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Block Material Amount", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "bar_material_details", "fieldname": "section_break_ampr", "fieldtype": "Section Break", "label": "Velocetec Line Bar Material"}, {"fieldname": "bar_material_details", "fieldtype": "Table", "options": "Velocetec Line Bar Material Detail", "search_index": 1}, {"depends_on": "bar_material_details", "fieldname": "section_break_6jr2", "fieldtype": "Section Break"}, {"fieldname": "spacer_2", "fieldtype": "HTML"}, {"fieldname": "column_break_8kuq", "fieldtype": "Column Break"}, {"fieldname": "bar_material_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bar Material Amount", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "routing_material_details", "fieldname": "velocetec_line_routing_section", "fieldtype": "Section Break", "label": "Velocetec Line Routing"}, {"fieldname": "routing_template", "fieldtype": "Link", "label": "Routing <PERSON><PERSON><PERSON>", "options": "Routing"}, {"fieldname": "routing_material_details", "fieldtype": "Table", "options": "Velocetec Line Routing Detail", "search_index": 1}, {"collapsible": 1, "collapsible_depends_on": "summary_details", "fieldname": "velocetec_routing_summary_section", "fieldtype": "Section Break", "label": "Velocetec Routing Summary ", "read_only": 1}, {"fieldname": "summary_details", "fieldtype": "Table", "options": "Velocetec Line Routing Summary Detail", "read_only": 1, "search_index": 1}, {"collapsible": 1, "collapsible_depends_on": "qtyload", "fieldname": "section_break_8fyy", "fieldtype": "Section Break", "label": "Mass Finishing"}, {"fieldname": "qtyload", "fieldtype": "Float", "label": "Qty/load"}, {"fieldname": "column_break_dit9", "fieldtype": "Column Break"}, {"fieldname": "no_of_loads", "fieldtype": "Float", "label": "No of Loads", "read_only": 1}, {"fieldname": "section_break_bqkf", "fieldtype": "Section Break"}, {"fieldname": "html_vfuh", "fieldtype": "HTML"}, {"fieldname": "column_break_oqpn", "fieldtype": "Column Break"}, {"fieldname": "total_mass_finishing", "fieldtype": "Data", "hidden": 1, "label": "Total Mass Finishing Cost", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "line_fixings", "fieldname": "section_break_awcy", "fieldtype": "Section Break", "label": "Fixings"}, {"fieldname": "line_fixings", "fieldtype": "Table", "options": "Velocetec Line Fixing Detail", "search_index": 1}, {"depends_on": "line_fixings", "fieldname": "section_break_la6n", "fieldtype": "Section Break"}, {"fieldname": "spacer_4", "fieldtype": "HTML"}, {"fieldname": "column_break_olro", "fieldtype": "Column Break"}, {"fieldname": "total_fixings_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Fixings Cost", "read_only": 1}, {"fieldname": "section_break_xdkc", "fieldtype": "Section Break"}, {"fieldname": "machining", "fieldtype": "Data", "hidden": 1, "label": "Machining", "read_only": 1}, {"fieldname": "finishing", "fieldtype": "Data", "hidden": 1, "label": "Finishing", "read_only": 1}, {"fieldname": "inspection", "fieldtype": "Data", "hidden": 1, "label": "Inspection", "read_only": 1}, {"fieldname": "other", "fieldtype": "Data", "hidden": 1, "label": "Other", "read_only": 1}, {"fieldname": "edm", "fieldtype": "Data", "hidden": 1, "label": "EDM", "read_only": 1}, {"fieldname": "tool_making", "fieldtype": "Data", "hidden": 1, "label": "Tool Making", "read_only": 1}, {"fieldname": "fixings", "fieldtype": "Data", "hidden": 1, "label": "Fixings"}, {"fieldname": "column_break_lsyq", "fieldtype": "Column Break"}, {"fieldname": "sub_con", "fieldtype": "Data", "hidden": 1, "label": "Sub Con", "read_only": 1}, {"fieldname": "design", "fieldtype": "Data", "hidden": 1, "label": "Design", "read_only": 1}, {"fieldname": "assembly", "fieldtype": "Data", "hidden": 1, "label": "Assembly", "read_only": 1}, {"fieldname": "turning", "fieldtype": "Data", "hidden": 1, "label": "Turning", "read_only": 1}, {"fieldname": "mass_finishing", "fieldtype": "Data", "hidden": 1, "label": "Mass Finishing", "read_only": 1}, {"fieldname": "material_price", "fieldtype": "Data", "hidden": 1, "label": "Material Price"}, {"collapsible": 1, "fieldname": "notes_section", "fieldtype": "Section Break", "label": "Notes"}, {"fieldname": "internal_notes", "fieldtype": "Text", "label": "Internal Notes"}, {"fieldname": "column_break_mxqi", "fieldtype": "Column Break"}, {"fieldname": "external_notes", "fieldtype": "Text", "label": "External Notes"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-09 07:58:10.861889", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Line Costing", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "search_fields": "velocetec_costing,velocetec_costing_detail,part_number", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "part_number", "track_changes": 1}