{"actions": [], "allow_rename": 1, "autoname": "format:VCM-{YYYY}-{#####}", "creation": "2024-11-26 11:53:05.094332", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["material_grade", "material_group", "description", "column_break_2hyeq", "scaling_factor", "price_per_kg", "cubic_meter_price"], "fields": [{"fieldname": "material_grade", "fieldtype": "Data", "in_list_view": 1, "label": "Material Grade"}, {"fieldname": "material_group", "fieldtype": "Data", "in_list_view": 1, "label": "Material Group"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "column_break_2hyeq", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "scaling_factor", "fieldtype": "Float", "label": "Scaling Factor"}, {"default": "0.0", "fieldname": "price_per_kg", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price Per Kg"}, {"fieldname": "cubic_meter_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON> Meter Price"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-18 03:14:49.112368", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Costing Material", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "search_fields": "material_grade, material_group", "sort_field": "modified", "sort_order": "DESC", "states": []}