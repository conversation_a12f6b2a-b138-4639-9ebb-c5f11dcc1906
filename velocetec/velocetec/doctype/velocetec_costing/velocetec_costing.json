{"actions": [], "allow_rename": 1, "autoname": "format:VC-{YYYY}-{#####}", "creation": "2024-11-26 12:06:32.802646", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "posting_date", "quotation_link_status", "column_break_txky", "quotation_to", "customer", "customer_primary_contact", "lead", "create_quotation", "section_break_b40x", "html", "column_break_qqop", "grand_total", "totals_section", "items", "section_break_omtd", "delivery_type", "column_break_e1jt", "delivery_cost", "column_break_n5qg", "no_of_shipments", "column_break_enzx", "delivery_total", "column_break_65ox", "add_to_parts", "notes_section", "internal_notes", "column_break_stzc", "external_notes", "section_break_jtfe", "reference_doctype", "quotation_version", "reference_opportunity", "column_break_yp5b", "reference_name", "amended_from", "is_amended"], "fields": [{"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date"}, {"fieldname": "quotation_link_status", "fieldtype": "Select", "label": "Quotation Link Status", "no_copy": 1, "options": "UnLinked\nLinked", "read_only": 1}, {"fieldname": "column_break_txky", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.quotation_to == \"Customer\"", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "link_filters": "[]", "options": "Customer"}, {"depends_on": "eval: doc.quotation_link_status == \"UnLinked\"", "fieldname": "create_quotation", "fieldtype": "<PERSON><PERSON>", "label": "Create Quotation "}, {"depends_on": "eval:(doc.customer != null && doc.quotation_to != \"Lead\")", "fieldname": "customer_primary_contact", "fieldtype": "Link", "label": "Contact", "options": "Contact"}, {"fieldname": "section_break_b40x", "fieldtype": "Section Break"}, {"fieldname": "html", "fieldtype": "HTML"}, {"fieldname": "column_break_qqop", "fieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"fieldname": "totals_section", "fieldtype": "Section Break"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "options": "Velocetec Costing Detail", "search_index": 1}, {"fieldname": "section_break_omtd", "fieldtype": "Section Break"}, {"fieldname": "delivery_type", "fieldtype": "Link", "label": "Delivery Type", "options": "Shipping Rule"}, {"fieldname": "column_break_e1jt", "fieldtype": "Column Break"}, {"fetch_from": "delivery_type.shipping_amount", "fieldname": "delivery_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delivery Cost"}, {"fieldname": "column_break_n5qg", "fieldtype": "Column Break"}, {"fieldname": "no_of_shipments", "fieldtype": "Int", "label": "No of Shipments"}, {"fieldname": "column_break_enzx", "fieldtype": "Column Break"}, {"fieldname": "delivery_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delivery Total", "read_only": 1}, {"fieldname": "column_break_65ox", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "add_to_parts", "fieldtype": "Check", "label": "Add to Parts?"}, {"fieldname": "section_break_jtfe", "fieldtype": "Section Break"}, {"allow_on_submit": 1, "fieldname": "reference_doctype", "fieldtype": "Data", "label": "Reference Doctype", "no_copy": 1, "read_only": 1}, {"fieldname": "quotation_version", "fieldtype": "Text", "hidden": 1, "label": "Quotation Version", "no_copy": 1, "read_only": 1}, {"fieldname": "reference_opportunity", "fieldtype": "Data", "label": "Reference Opportunity ", "read_only": 1}, {"fieldname": "column_break_yp5b", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "reference_name", "fieldtype": "Data", "label": "Reference Name", "no_copy": 1, "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Velocetec Costing", "print_hide": 1, "read_only": 1, "search_index": 1}, {"default": "0", "fieldname": "is_amended", "fieldtype": "Check", "hidden": 1, "label": "Is Amended", "no_copy": 1, "read_only": 1, "set_only_once": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "quotation_to", "fieldtype": "Link", "label": "Quotation To", "options": "DocType"}, {"depends_on": "eval:doc.quotation_to == \"Lead\"", "fieldname": "lead", "fieldtype": "Link", "label": "Lead", "options": "Lead"}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.internal_notes && doc.external_notes", "fieldname": "notes_section", "fieldtype": "Section Break", "label": "Notes"}, {"fieldname": "internal_notes", "fieldtype": "Text", "label": "Internal Notes"}, {"fieldname": "column_break_stzc", "fieldtype": "Column Break"}, {"fieldname": "external_notes", "fieldtype": "Text", "label": "External Notes"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-24 12:33:03.330950", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Costing", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "select": 1, "share": 1, "write": 1}], "row_format": "Dynamic", "search_fields": "reference_name,reference_doctype,quotation_link_status", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}