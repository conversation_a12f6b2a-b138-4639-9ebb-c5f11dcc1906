# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
import json
import math
from frappe import _
from frappe.model.document import Document


@frappe.whitelist()
def simple_rollback(docname):
    """Simple server-side method to rollback the current transaction"""
    try:
        # Rollback the current transaction
        frappe.db.rollback()

        # Return success
        return {"success": True}
    except Exception as e:
        frappe.log_error(f"Error in simple rollback: {str(e)}", "Rollback Error")
        return {"success": False}


@frappe.whitelist()
def check_part_has_raw_materials(item_name, has_children=False):
    """Check if a part has raw materials in its VLC

    Args:
        item_name (str): The name of the Velocetec Costing Detail item
        has_children (bool): Whether the item has children

    Returns:
        bool: True if the part has raw materials, False otherwise
    """
    try:
        # If the item has children, it's considered to have "raw materials" through its children
        if has_children and frappe.utils.cint(has_children) == 1:
            return True

        # Get the VLC document linked to this item
        vlc = frappe.db.get_value(
            "Velocetec Line Costing", {"velocetec_costing_detail": item_name}, "name"
        )

        if not vlc:
            return False

        # Check if there are any block materials
        block_count = frappe.db.count(
            "Velocetec Line Block Material Detail", {"parent": vlc}
        )

        # Check if there are any bar materials
        bar_count = frappe.db.count(
            "Velocetec Line Bar Material Detail", {"parent": vlc}
        )

        # Check if there are any fixings materials
        fixings_count = frappe.db.count(
            "Velocetec Line Fixing Detail", {"parent": vlc}
        )

        # Return True if any of block, bar, or fixings materials exist
        return block_count > 0 or bar_count > 0 or fixings_count > 0

    except Exception as e:
        frappe.log_error(
            f"Error checking for raw materials: {str(e)}", "Raw Materials Check Error"
        )
        return False


def safe_float(value, default=0):
    """Safely convert a value to float, returning default if conversion fails."""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


class VelocetecCosting(Document):

    def before_save(self):
        """Final check before saving to ensure parent items have the correct sell_price_each values."""
        # Check if any items need recalculation
        needs_recalculation = False
        for item in self.items:
            if hasattr(item, "needs_recalculation") and item.needs_recalculation:
                needs_recalculation = True
                break

        # If no items need recalculation and this is not a new document, skip the calculation
        if not needs_recalculation and not self.is_new():
            return
        # STEP 1: Calculate base costs for each item (without children's costs)
        for item in self.items:
            # Calculate the base cost for this item (its own cost without children)
            base_cost = (
                safe_float(item.material_price)
                + safe_float(item.machining)
                + safe_float(item.design)
                + safe_float(item.finishing)
                + safe_float(item.inspection)
                + safe_float(item.other)
                + safe_float(item.mass_finishing)
                + safe_float(item.sub_con)
                + safe_float(item.fixings)
                + safe_float(item.tool_making)
                + safe_float(item.turning)
                + safe_float(item.edm)
                + safe_float(item.assembly)
                + safe_float(item.delivery_each)
            )

            # Store the base cost in a temporary property
            item._base_cost = math.ceil(base_cost)

            # Initialize total cost (will be updated in the next step)
            item._total_cost = base_cost

            # Mark all items as not processed yet
            item._processed = False

        # STEP 2: Build a map of parent-child relationships for faster lookup
        children_map = {}
        for item in self.items:
            if item.is_child_part:
                # Determine the parent ID
                parent_id = item.parent_id
                if not parent_id and item.parent_part:
                    # Find the parent by part_number
                    parent = next(
                        (p for p in self.items if p.part_number == item.parent_part),
                        None,
                    )
                    if parent:
                        parent_id = parent.name

                if parent_id:
                    if parent_id not in children_map:
                        children_map[parent_id] = []
                    children_map[parent_id].append(item.name)

        # STEP 3: Calculate total costs recursively through the hierarchy
        def calculate_total_cost(item_name):
            item = next((i for i in self.items if i.name == item_name), None)
            if not item or item._processed:
                return item._total_cost if item else 0

            # Mark as processed to avoid infinite recursion
            item._processed = True

            # Start with the item's own base cost
            total_cost = math.ceil(item._base_cost)

            # Add costs of all children recursively, accounting for their quantities
            children = children_map.get(item_name, [])
            for child_name in children:
                child = next((i for i in self.items if i.name == child_name), None)
                if child:
                    child_cost = calculate_total_cost(child_name)
                    # Add the child's contribution (base cost * quantity) with ceiling rounding
                    child_contribution = math.ceil(
                        child_cost * safe_float(child.quantity)
                    )
                    total_cost += child_contribution

            # Store the total cost
            item._total_cost = total_cost

            # Update the item's cost fields
            item.each_cost = total_cost

            # For child items, sell_price_each should only include their own cost
            # For parent items, sell_price_each will be calculated later using the correct formula:
            # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity
            if item.is_child_part:
                # For child items, only include their own cost
                # Round up sell_price_each to whole number (integer)
                item.sell_price_each = math.ceil(
                    math.ceil(item._base_cost)
                    * (1 + (safe_float(item.mark_up_item) / 100))
                )
            # Note: Parent items' sell_price_each will be calculated in STEP 6 using the correct formula

            # Batch cost is always based on the total cost
            item.batch_cost = safe_float(item.quantity) * total_cost

            # Calculate line_total based on the item's own sell_price_each and quantity
            # This ensures we don't double-count children's costs in the line_total
            item.line_total = safe_float(item.quantity) * item.sell_price_each

            return total_cost

        # STEP 4: Process all items in bottom-up order (children first, then parents)
        # First, process all leaf nodes (items without children)
        for item in self.items:
            has_children = False
            for child in self.items:
                if child.is_child_part:
                    if (child.parent_id and child.parent_id == item.name) or (
                        not child.parent_id and child.parent_part == item.part_number
                    ):
                        has_children = True
                        break

            if not has_children:
                calculate_total_cost(item.name)

        # Then process items with children, in multiple passes if needed
        unprocessed_items = True
        pass_count = 0
        max_passes = 10  # Safety limit

        while unprocessed_items and pass_count < max_passes:
            pass_count += 1
            unprocessed_items = False

            for item in self.items:
                if not getattr(item, "_processed", False):
                    # Check if all children are processed
                    children = []
                    for child in self.items:
                        if child.is_child_part:
                            if (child.parent_id and child.parent_id == item.name) or (
                                not child.parent_id
                                and child.parent_part == item.part_number
                            ):
                                children.append(child)

                    all_children_processed = True
                    for child in children:
                        if not getattr(child, "_processed", False):
                            all_children_processed = False
                            break

                    if all_children_processed:
                        calculate_total_cost(item.name)
                    else:
                        unprocessed_items = True

        # Final pass for any remaining items
        for item in self.items:
            if not getattr(item, "_processed", False):
                calculate_total_cost(item.name)

        # STEP 5: Calculate line_total using hierarchical structure
        # First calculate each item's own line_total (without children)
        for item in self.items:
            # Calculate each item's own line_total based on its own cost
            # Round up line_total to whole number
            item.line_total = math.ceil(
                safe_float(item.quantity)
                * (
                    math.ceil(item._base_cost)
                    * (1 + (safe_float(item.mark_up_item) / 100))
                )
            )

            # Store the original line_total for hierarchical calculation
            item._own_line_total = item.line_total

        # Define a recursive function to calculate hierarchical line_total
        def calculate_hierarchical_line_total(item_id):
            item = next((i for i in self.items if i.name == item_id), None)
            if not item:
                return 0

            # Start with the item's own line_total
            total = safe_float(item._own_line_total)

            # Find all direct children of this item
            children = []
            for child in self.items:
                if child.is_child_part:
                    if (child.parent_id and child.parent_id == item.name) or (
                        not child.parent_id and child.parent_part == item.part_number
                    ):
                        children.append(child)

            # Add each child's hierarchical line_total (recursively)
            for child in children:
                total += calculate_hierarchical_line_total(child.name)

            # Update the item's line_total with the hierarchical total
            item.line_total = total

            return total

        # Apply the recursive calculation to all top-level items
        for item in self.items:
            if not item.is_child_part:
                calculate_hierarchical_line_total(item.name)

        # STEP 6: Calculate sell_price_each and sell_cost_each according to the new requirements
        # First, calculate base sell_price_each and base sell_cost_each for all items (their own cost without children)
        for item in self.items:
            # Store the base sell price with markup (without children) for later use
            item._own_sell_price_each = math.ceil(item._base_cost) * (
                1 + (safe_float(item.mark_up_item) / 100)
            )

            # Store the base sell cost (from VLC) without children for later use in the own_sell_cost_each field
            # ONLY use VLC's sell_cost_each - do NOT use any calculated base cost
            if not item.own_sell_cost_each:
                item.own_sell_cost_each = (
                    safe_float(item.sell_cost_each)
                    if hasattr(item, "sell_cost_each") and item.sell_cost_each
                    else 0  # If no VLC sell_cost_each, use 0 instead of any calculated value
                )

            # Set temporary variable _own_sell_cost_each from the persistent field for calculations
            item._own_sell_cost_each = safe_float(item.own_sell_cost_each)

        # Process items in reverse order (bottom-up) to ensure children are processed before parents
        items_in_reverse_order = sorted(
            self.items, key=lambda x: safe_float(x.idx), reverse=True
        )

        # Helper function to get all direct children of an item
        def get_direct_children(parent_item):
            children = []
            for child in self.items:
                if child.is_child_part:
                    # Check parent_id relationship
                    if child.parent_id and child.parent_id == parent_item.name:
                        children.append(child)
                    # Check parent_part relationship
                    elif (
                        not child.parent_id
                        and child.parent_part == parent_item.part_number
                    ):
                        children.append(child)
            return children

        # Process all items in reverse order (bottom-up)
        for item in items_in_reverse_order:
            if item.is_child_part:
                # For child items that are also parents (middle-level items)
                children = get_direct_children(item)
                if children:
                    # Middle-level item's sell_price_each should be its own base selling price plus all its children's line_total
                    # (children's line_totals accumulate to parent's sell_price_each)
                    item.sell_price_each = math.ceil(item._own_sell_price_each)

                    # Add each child's line_total (which already accounts for quantity and hierarchical structure)
                    for child in children:
                        item.sell_price_each += safe_float(child.line_total)
                else:
                    # For leaf nodes (items without children), just use their own sell price
                    item.sell_price_each = math.ceil(item._own_sell_price_each)
            else:
                # For root nodes (top-level parent items)
                # Root's sell_price_each should be the sum of all direct children's contributions
                children = get_direct_children(item)

                if children:
                    # If the root node has children, calculate using the correct formula:
                    # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity

                    # Calculate parent's own sell price × quantity
                    parent_own_total = math.ceil(
                        item._own_sell_price_each
                    ) * safe_float(item.quantity)

                    # Sum all children's line_totals
                    children_line_total_sum = 0
                    for child in children:
                        children_line_total_sum += safe_float(child.line_total)

                    # Calculate final sell_price_each using the correct formula
                    total_value = parent_own_total + children_line_total_sum
                    item.sell_price_each = (
                        math.ceil(total_value / safe_float(item.quantity))
                        if safe_float(item.quantity) > 0
                        else 0
                    )
                else:
                    # If the root node has no children, use its own sell price
                    # This handles standalone root items
                    item.sell_price_each = math.ceil(item._own_sell_price_each)

    def on_update(self):
        """Final check after saving to ensure all items have the correct sell_price_each values."""
        try:
            # Create a dictionary to map item names to item objects for faster lookup
            item_map = {item.name: item for item in self.items}

            # STEP 1: Calculate base costs for each item (without children's costs)
            for item in self.items:
                # Calculate the base cost for this item (its own cost without children)
                base_cost = (
                    safe_float(item.material_price)
                    + safe_float(item.machining)
                    + safe_float(item.design)
                    + safe_float(item.finishing)
                    + safe_float(item.inspection)
                    + safe_float(item.other)
                    + safe_float(item.mass_finishing)
                    + safe_float(item.sub_con)
                    + safe_float(item.fixings)
                    + safe_float(item.tool_making)
                    + safe_float(item.turning)
                    + safe_float(item.edm)
                    + safe_float(item.assembly)
                    + safe_float(item.delivery_each)
                )

                # Store temporary values directly on the item object
                item._base_cost = math.ceil(base_cost)
                item._total_cost = base_cost
                item._processed = False

            # STEP 2: Build a map of parent-child relationships for faster lookup
            children_map = {}
            for item in self.items:
                if item.is_child_part:
                    # Determine the parent ID
                    parent_id = item.parent_id
                    if not parent_id and item.parent_part:
                        # Find the parent by part_number
                        parent = next(
                            (
                                p
                                for p in self.items
                                if p.part_number == item.parent_part
                            ),
                            None,
                        )
                        if parent:
                            parent_id = parent.name

                    if parent_id:
                        if parent_id not in children_map:
                            children_map[parent_id] = []
                        children_map[parent_id].append(item.name)

            # STEP 3: Calculate total costs recursively through the hierarchy
            def calculate_total_cost(item_id):
                # Get the item directly from our item map
                item = item_map.get(item_id)
                if not item or getattr(item, "_processed", False):
                    return getattr(item, "_total_cost", 0) if item else 0

                # Mark as processed to avoid infinite recursion
                item._processed = True

                # Start with the item's own base cost
                total_cost = math.ceil(item._base_cost)

                # Add costs of all children recursively, accounting for their quantities
                children = children_map.get(item_id, [])
                for child_id in children:
                    child_item = item_map.get(child_id)
                    if child_item:
                        child_cost = calculate_total_cost(child_id)
                        # Add the child's contribution (base cost * quantity) with ceiling rounding
                        child_contribution = math.ceil(
                            child_cost * safe_float(child_item.quantity)
                        )
                        total_cost += child_contribution

                # Store the total cost directly on the item
                item._total_cost = total_cost

                # Update the item's cost fields
                item.each_cost = total_cost

                # For child items, sell_price_each should only include their own cost
                # For parent items, sell_price_each will be calculated later using the correct formula:
                # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity
                if item.is_child_part:
                    # For child items, only include their own cost
                    # Round up sell_price_each to whole number (integer)
                    item.sell_price_each = math.ceil(
                        math.ceil(item._base_cost)
                        * (1 + (safe_float(item.mark_up_item) / 100))
                    )
                # Note: Parent items' sell_price_each will be calculated in STEP 6 using the correct formula

                # Batch cost is always based on the total cost
                item.batch_cost = safe_float(item.quantity) * total_cost

                # Calculate line_total based on the item's own sell_price_each and quantity
                # Round up line_total to whole number
                item.line_total = math.ceil(
                    safe_float(item.quantity) * item.sell_price_each
                )

                return total_cost

            # STEP 4: Process all items in bottom-up order (children first, then parents)
            # First, process all leaf nodes (items without children)
            for item in self.items:
                if item.name not in children_map:  # No children
                    calculate_total_cost(item.name)

            # Then process items with children, in multiple passes if needed
            unprocessed_items = True
            pass_count = 0
            max_passes = 10  # Safety limit

            while unprocessed_items and pass_count < max_passes:
                pass_count += 1
                unprocessed_items = False

                for item in self.items:
                    if not getattr(item, "_processed", False):
                        # Check if all children are processed
                        children = children_map.get(item.name, [])
                        all_children_processed = True

                        for child_id in children:
                            child = item_map.get(child_id)
                            if child and not getattr(child, "_processed", False):
                                all_children_processed = False
                                break

                        if all_children_processed:
                            calculate_total_cost(item.name)
                        else:
                            unprocessed_items = True

            # Final pass for any remaining items
            for item in self.items:
                if not getattr(item, "_processed", False):
                    calculate_total_cost(item.name)

            # STEP 5: Calculate line_total using hierarchical structure
            # First calculate each item's own line_total (without children)
            for item in self.items:
                # Calculate the item's own line_total based on its own cost
                # Round up line_total to whole number
                own_line_total = math.ceil(
                    safe_float(item.quantity)
                    * (
                        math.ceil(item._base_cost)
                        * (1 + (safe_float(item.mark_up_item) / 100))
                    )
                )

                # Store the original line_total directly on the item
                item._own_line_total = own_line_total

            # Define a recursive function to calculate hierarchical line_total
            def calculate_hierarchical_line_total(item_id):
                # Get the item directly from our item map
                item = item_map.get(item_id)
                if not item:
                    return 0

                # Start with the item's own line_total
                total = getattr(item, "_own_line_total", 0)

                # Find all direct children of this item
                children = children_map.get(item_id, [])

                # Add each child's hierarchical line_total (recursively)
                for child_id in children:
                    total += calculate_hierarchical_line_total(child_id)

                # Update the item's line_total with the hierarchical total
                item.line_total = total

                return total

            # Apply the recursive calculation to all top-level items
            for item in self.items:
                if not item.is_child_part:
                    calculate_hierarchical_line_total(item.name)

            # STEP 6: Calculate sell_price_each according to the new requirements
            # First, calculate base sell_price_each for all items (their own cost without children)
            for item in self.items:
                # Calculate the base sell price (without children)
                own_sell_price_each = math.ceil(item._base_cost) * (
                    1 + (safe_float(item.mark_up_item) / 100)
                )

                # Store the base sell price directly on the item
                item._own_sell_price_each = math.ceil(own_sell_price_each)

            # Process items in reverse order (bottom-up) to ensure children are processed before parents
            items_in_reverse_order = sorted(
                self.items, key=lambda x: safe_float(x.idx), reverse=True
            )

            # Helper function to get all direct children of an item
            def get_direct_children(parent_item):
                children = []
                for child in self.items:
                    if child.is_child_part:
                        # Check parent_id relationship
                        if child.parent_id and child.parent_id == parent_item.name:
                            children.append(child)
                        # Check parent_part relationship
                        elif (
                            not child.parent_id
                            and child.parent_part == parent_item.part_number
                        ):
                            children.append(child)
                return children

            # Process all items in reverse order (bottom-up)
            for item in items_in_reverse_order:
                if item.is_child_part:
                    # For child items that are also parents (middle-level items)
                    children = get_direct_children(item)

                    if children:
                        # Middle-level item's sell_price_each should be its own base selling price plus all its children's line_total
                        sell_price_each = math.ceil(item._own_sell_price_each)

                        # Add each child's line_total (which already accounts for quantity and hierarchical structure)
                        for child in children:
                            # For sell_price_each, add child's line_total (with markup)
                            sell_price_each += safe_float(child.line_total)

                        # Update the sell_price_each
                        item.sell_price_each = sell_price_each
                        # Note: sell_cost_each hierarchical aggregation is handled in validate() method
                    else:
                        # For leaf nodes (items without children), just use their own sell price
                        item.sell_price_each = math.ceil(item._own_sell_price_each)
                        # Note: sell_cost_each for leaf nodes is handled in validate() method
                else:
                    # For root nodes (top-level parent items)
                    # Root's sell_price_each should be the sum of all direct children's contributions
                    children = get_direct_children(item)

                    if children:
                        # If the root node has children, calculate using the correct formula:
                        # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity

                        # Calculate parent's own sell price × quantity
                        parent_own_total = math.ceil(
                            item._own_sell_price_each
                        ) * safe_float(item.quantity)

                        # Sum all children's line_totals
                        children_line_total_sum = 0
                        children_cost_total_sum = 0

                        for child in children:
                            # Add child's line_total (with markup)
                            children_line_total_sum += safe_float(child.line_total)

                            # For sell_cost_each, sum children's sell_cost_each directly (same pattern as sell_price_each uses line_total)
                            children_cost_total_sum += safe_float(child.sell_cost_each)

                        # Calculate final sell_price_each using the correct formula
                        total_value = parent_own_total + children_line_total_sum
                        item.sell_price_each = (
                            math.ceil(total_value / safe_float(item.quantity))
                            if safe_float(item.quantity) > 0
                            else 0
                        )

                        # Note: sell_cost_each hierarchical aggregation is handled in validate() method
                        # to avoid duplication and doubling issues
                    else:
                        # If the root node has no children, use its own sell price
                        item.sell_price_each = math.ceil(item._own_sell_price_each)
                        # Note: sell_cost_each for items without children is handled in validate() method

            # Save the document to persist changes
            self.db_update()

            # IMPORTANT: Also update each child item individually to ensure they're saved
            for item in self.items:
                frappe.db.set_value("Velocetec Costing Detail", item.name, "sell_price_each", item.sell_price_each)

            frappe.db.commit()
        except Exception as e:
            frappe.log_error(f"Error updating item values in on_update: {str(e)}")

    def create_vlc_safely(self, data, item):
        """Create a VLC document safely during duplication process."""
        try:
            # Create a new VLC document
            new_vlc = frappe.new_doc("Velocetec Line Costing")
            new_vlc._skip_validation = True

            # Remove fields that might cause issues during duplication
            for field in [
                "name",
                "creation",
                "modified",
                "modified_by",
                "owner",
                "docstatus",
                "idx",
            ]:
                if field in data:
                    del data[field]

            # Update with the data
            new_vlc.update(data)

            # Ensure the VLC is properly linked to the VCD
            new_vlc.velocetec_costing_detail = item.name
            new_vlc.velocetec_costing = self.name
            new_vlc.part_number = item.part_number

            # Save the VCD first to ensure it exists
            item.db_update()
            frappe.db.commit()

            # Insert the new VLC
            new_vlc.insert(
                ignore_permissions=True, ignore_mandatory=True, ignore_links=True
            )

            # Mark the item as having a created VLC to prevent duplicate creation
            item.is_dup_reference_created = True
            item.db_update()

            # Update the velocetec_line_costing field with the new VLC data
            item.velocetec_line_costing = json.dumps(
                new_vlc.as_dict(no_nulls=True), default=str
            )
            item.db_update()

            return new_vlc
        except Exception as e:
            frappe.log_error(
                "VLC Creation Error",
                f"Error creating VLC during duplication: {str(e)}\nData: {data}\nItem: {item.name}",
            )
            return None

    def validate(self):

        if self.quotation_to == "Lead":
            self.customer = None
            self.customer_primary_contact = None

        if self.quotation_to == "Customer":
            self.lead = None

        # Check if document is linked and changes are not allowed
        if self.quotation_link_status == "Linked" and not frappe.flags.get(
            "ignore_vc_link_validation"
        ):
            # Throw an error unless override is set
            if not frappe.flags.get("allow_linked_vc_changes"):
                frappe.throw(
                    _(
                        "This Velocetec Costing document is linked to a quotation. Changes are not allowed. "
                        "Use the 'Rollback Changes' button to discard your changes."
                    )
                )

        # Validate tree structure integrity
        self.validate_tree_structure()

        # Validate delivery date hierarchy
        self.validate_delivery_date_hierarchy()

        for item in self.items:
            if item.is_child_part and not item.parent_part:
                frappe.throw(
                    _(f"Parent Part is required for Child Parts {item.part_number}")
                )

            # Clear parent_part if not a child part
            if not item.is_child_part and item.parent_part:
                item.parent_part = ""
                item.parent_id = ""

    def validate_tree_structure(self):
        """Validate the integrity of the tree structure"""
        try:
            # Create a mapping of part_number to item
            part_number_map = {}
            for item in self.items:
                if item.part_number:
                    part_number_map[item.part_number] = item

            for item in self.items:
                if item.is_child_part and item.parent_part:
                    if item.parent_part in part_number_map:
                        parent_item = part_number_map[item.parent_part]
                        if item.parent_id != parent_item.name:
                            item.parent_id = parent_item.name
                    else:
                        frappe.log_error(
                            message=f"Child {item.part_number} references parent {item.parent_part} that doesn't exist in document {self.name}",
                            title="Tree Reference Warning",
                        )

            # Calculate sell_price_each for parent items
            def calculate_parent_sell_price_each():
                # First, calculate base sell_price_each for all items (their own cost without children)
                for item in self.items:
                    # Store the base sell price (without children) for later use
                    item._own_sell_price_each = (
                        math.ceil(item._base_cost)
                        * (1 + (safe_float(item.mark_up_item) / 100))
                        if hasattr(item, "_base_cost")
                        else item.machining
                    )

                    # Initialize sell_price_each with the base value
                    item.sell_price_each = math.ceil(item._own_sell_price_each)

                # Process items in reverse order (bottom-up) to ensure children are processed before parents
                items_in_reverse_order = sorted(
                    self.items, key=lambda x: safe_float(x.idx), reverse=True
                )

                # Helper function to get all direct children of an item
                def get_direct_children(parent_item):
                    children = []
                    for child in self.items:
                        if child.is_child_part:
                            # Check parent_id relationship
                            if child.parent_id and child.parent_id == parent_item.name:
                                children.append(child)
                            # Check parent_part relationship
                            elif (
                                not child.parent_id
                                and child.parent_part == parent_item.part_number
                            ):
                                children.append(child)

                    return children

                # Process all items in reverse order (bottom-up)
                for item in items_in_reverse_order:
                    if item.is_child_part:
                        # For child items that are also parents (middle-level items)
                        children = get_direct_children(item)
                        if children:
                            # Middle-level item's sell_price_each should be its own base selling price plus all its children's line_total
                            # (children's line_totals accumulate to parent's sell_price_each)
                            item.sell_price_each = math.ceil(item._own_sell_price_each)

                            # Add each child's line_total (which already accounts for quantity and hierarchical structure)
                            for child in children:
                                item.sell_price_each += safe_float(child.line_total)
                        else:
                            # For leaf nodes (items without children), just use their own sell price
                            item.sell_price_each = math.ceil(item._own_sell_price_each)
                    else:
                        # For root nodes (top-level parent items)
                        # Root's sell_price_each should be the sum of all direct children's contributions
                        children = get_direct_children(item)

                        if children:
                            # If the root node has children, calculate using the correct formula:
                            # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity

                            # Calculate parent's own sell price × quantity
                            parent_own_total = math.ceil(
                                item._own_sell_price_each
                            ) * safe_float(item.quantity)

                            # Sum all children's line_totals
                            children_line_total_sum = 0
                            for child in children:
                                children_line_total_sum += safe_float(child.line_total)

                            # Calculate final sell_price_each using the correct formula
                            total_value = parent_own_total + children_line_total_sum
                            item.sell_price_each = (
                                math.ceil(total_value / safe_float(item.quantity))
                                if safe_float(item.quantity) > 0
                                else 0
                            )
                        else:
                            # If the root node has no children, use its own sell price
                            # This handles standalone root items
                            item.sell_price_each = math.ceil(item._own_sell_price_each)

                    # Don't update line_total here - it will be calculated separately using hierarchical structure

                # No special handling needed - the generic algorithm above handles all cases

            # Apply the calculation to get correct sell_price_each values for parent items
            if hasattr(self.items[0], "_base_cost"):
                calculate_parent_sell_price_each()

                # Calculate line_total using hierarchical structure
                # First calculate each item's own line_total (without children)
                for item in self.items:
                    # Calculate each item's own line_total based on its own cost
                    # Round up line_total to whole number
                    item.line_total = math.ceil(
                        safe_float(item.quantity)
                        * (
                            safe_float(math.ceil(item._base_cost))
                            * (1 + (safe_float(item.mark_up_item) / 100))
                        )
                    )

                    # Store the original line_total for hierarchical calculation
                    item._own_line_total = item.line_total

                # Define a recursive function to calculate hierarchical line_total
                def calculate_hierarchical_line_total(item_id):
                    item = next((i for i in self.items if i.name == item_id), None)
                    if not item:
                        return 0

                    # Start with the item's own line_total
                    total = safe_float(item._own_line_total)

                    # Find all direct children of this item
                    children = []
                    for child in self.items:
                        if child.is_child_part:
                            # Check parent_id relationship
                            if child.parent_id and child.parent_id == item.name:
                                children.append(child)
                            # Check parent_part relationship
                            elif (
                                not child.parent_id
                                and child.parent_part == item.part_number
                            ):
                                children.append(child)

                    # Add each child's hierarchical line_total (recursively)
                    for child in children:
                        total += calculate_hierarchical_line_total(child.name)

                    # Update the item's line_total with the hierarchical total
                    item.line_total = total

                    return total

                # Apply the recursive calculation to all top-level items
                for item in self.items:
                    if not item.is_child_part:
                        calculate_hierarchical_line_total(item.name)

        except Exception as e:
            frappe.log_error(
                message=f"Error validating tree structure for document {self.name}:\n{str(e)}",
                title="Tree Structure Error",
            )

    def validate_delivery_date_hierarchy(self):
        """
        Validate delivery date hierarchy across all items in the document.
        Ensures that child items' delivery dates are not later than their parent's delivery dates.
        """
        try:
            # Create a mapping of part_number to item for quick lookup
            part_number_map = {}
            for item in self.items:
                if item.part_number:
                    part_number_map[item.part_number] = item

            # Validate each child item's delivery date against its parent
            for item in self.items:
                if not item.is_child_part or not item.delivery_date:
                    continue

                # Find the parent item
                parent_item = None

                # Try to find parent by parent_id first (more reliable)
                if item.parent_id:
                    parent_item = next(
                        (i for i in self.items if i.name == item.parent_id), None
                    )

                # Fallback to finding parent by parent_part
                if not parent_item and item.parent_part:
                    parent_item = part_number_map.get(item.parent_part)

                # Skip validation if parent not found or parent has no delivery date
                if not parent_item or not parent_item.delivery_date:
                    continue

                # Convert dates for comparison
                from frappe.utils import getdate

                child_delivery_date = getdate(item.delivery_date)
                parent_delivery_date = getdate(parent_item.delivery_date)

                # Validate that child delivery date is not later than parent
                if child_delivery_date > parent_delivery_date:
                    frappe.throw(
                        _(
                            "Child item '{0}' delivery date ({1}) cannot be later than parent item '{2}' delivery date ({3})"
                        ).format(
                            item.part_number or "Unknown",
                            frappe.utils.formatdate(child_delivery_date),
                            parent_item.part_number or "Unknown",
                            frappe.utils.formatdate(parent_delivery_date),
                        ),
                        title=_("Invalid Delivery Date"),
                    )

        except Exception as e:
            frappe.log_error(
                message=f"Error validating delivery date hierarchy for {self.name}: {str(e)}",
                title="Delivery Date Validation Error",
            )

        # STEP 1: Process each line item and update from VLC if needed
        for item in self.items:
            # --- duplication logic ---
            vlc = None
            if (
                item.dup_reference_part_number
                and item.dup_reference_part_number_name
                and not item.is_dup_reference_created
            ):

                try:
                    old_vlc_name = frappe.db.get_value(
                        "Velocetec Line Costing",
                        {
                            "velocetec_costing_detail": item.dup_reference_part_number_name
                        },
                        "name",
                    )
                    if old_vlc_name:
                        # Check if the document exists before trying to get it
                        if frappe.db.exists("Velocetec Line Costing", old_vlc_name):
                            old_vlc = frappe.get_doc(
                                "Velocetec Line Costing", old_vlc_name
                            )
                            data = old_vlc.as_dict(no_nulls=True)
                            data.update(
                                {
                                    "part_number": item.part_number,
                                    "description": item.description,
                                    "velocetec_costing_detail": item.name,
                                    "velocetec_costing": self.name,
                                }
                            )
                            # Remove fields that might cause issues during duplication
                            for field in [
                                "name",
                                "creation",
                                "modified",
                                "modified_by",
                                "owner",
                                "docstatus",
                                "idx",
                            ]:
                                if field in data:
                                    del data[field]

                            # Use the safer method to create VLC during duplication
                            new_vlc = self.create_vlc_safely(data, item)

                            if new_vlc:
                                # Mark as created to prevent duplicate creation
                                item.is_dup_reference_created = True

                                # Store the VLC reference in the velocetec_line_costing field
                                item.velocetec_line_costing = json.dumps(
                                    new_vlc.as_dict(no_nulls=True), default=str
                                )

                                # Save the item to persist the changes
                                item.db_update()

                                # Set vlc for later use
                                vlc = new_vlc.as_dict(no_nulls=True)

                except Exception as e:
                    frappe.log_error(f"Error duplicating VLC for {item.name}: {str(e)}")

            # --- or existing VLC logic ---
            elif item.velocetec_line_costing:
                try:
                    stored = frappe.db.get_value(
                        "Velocetec Costing Detail",
                        {"name": item.name},
                        "velocetec_line_costing",
                    )
                    vlc_data = json.loads(stored or "{}")

                    # Check if the document exists before trying to get it
                    vlc_name = vlc_data.get("name")
                    if vlc_name and frappe.db.exists(
                        "Velocetec Line Costing", vlc_name
                    ):
                        vlc_doc = frappe.get_doc("Velocetec Line Costing", vlc_name)

                        # Ensure the part number in the VLC matches the current part number
                        if vlc_doc.part_number != item.part_number:
                            vlc_doc.part_number = item.part_number

                        vlc_doc.save()
                        # Get the document data as a dictionary
                        vlc = vlc_doc.as_dict(no_nulls=True)

                        # Update the velocetec_line_costing field with the latest VLC data
                        item.velocetec_line_costing = json.dumps(vlc, default=str)
                        item.db_update()
                except Exception as e:
                    frappe.log_error(
                        "VLC Processing Error",
                        f"Error processing VLC for {item.name}: {str(e)}",
                    )
                    continue

            # --- if we have a VLC, pull in its numbers ---
            if vlc:
                # copy cost fields
                for fld in (
                    "machining",
                    "finishing",
                    "other",
                    "inspection",
                    "mass_finishing",
                    "sub_con",
                    "tool_making",
                    "turning",
                    "design",
                    "edm",
                    "assembly",
                    "material_price",
                    "delivery_each",
                ):
                    # Ensure numeric values are stored as floats
                    value = safe_float(vlc.get(fld, 0))
                    setattr(item, fld, value)

                # Handle fixings separately - use total_fixings_cost from VLC
                if vlc.get("total_fixings_cost"):
                    item.fixings = safe_float(vlc.get("total_fixings_cost", 0))

                # recalc line costs - ensure all values are converted to float
                item.each_cost = sum(
                    [
                        safe_float(item.material_price),
                        safe_float(item.machining),
                        safe_float(item.design),
                        safe_float(item.finishing),
                        safe_float(item.inspection),
                        safe_float(item.other),
                        safe_float(item.mass_finishing),
                        safe_float(item.sub_con),
                        safe_float(item.fixings),
                        safe_float(item.tool_making),
                        safe_float(item.turning),
                        safe_float(item.edm),
                        safe_float(item.delivery_each),
                        safe_float(item.assembly),
                    ]
                )
                item.batch_cost = safe_float(item.quantity) * item.each_cost
                # Round up sell_price_each to whole number (integer)
                item.sell_price_each = math.ceil(
                    item.each_cost * (1 + (safe_float(item.mark_up_item) / 100))
                )

                item.delivery_total = safe_float(item.delivery_cost) * safe_float(
                    item.no_of_shipments
                )
                item.delivery_each = item.delivery_total / max(
                    safe_float(item.quantity), 1
                )
                # Don't update line_total here - it will be calculated separately using hierarchical structure

                item.parent_id = frappe.db.get_value(
                    item.doctype,
                    {"parent": item.parent, "part_number": item.parent_part},
                    "name",
                )

            # We'll handle add_to_parts in a separate step after the hierarchical calculation

        # STEP 2: Implement recursive hierarchical cost calculation
        numeric_fields = [
            "material_price",
            "machining",
            "design",
            "finishing",
            "inspection",
            "other",
            "mass_finishing",
            "sub_con",
            "fixings",
            "tool_making",
            "turning",
            "edm",
            "assembly",
            "delivery_each",
        ]

        # Calculate base costs for each item (without children's costs)
        for item in self.items:
            # Calculate the base cost for this item (its own cost without children)
            base_cost = sum(safe_float(getattr(item, f)) for f in numeric_fields)

            # Store the base cost in a temporary attribute
            item._base_cost = math.ceil(base_cost)

            # Initialize total cost (will be updated in the recursive calculation)
            item._total_cost = base_cost

            # Mark all items as not processed yet
            item._processed = False

            # Initialize own_sell_cost_each field with the original VLC value (before any hierarchical aggregation)
            # This ensures we always have the parent's own cost without children's costs
            # ONLY use VLC's sell_cost_each - do NOT use _base_cost for sell_cost_each calculations
            if not item.own_sell_cost_each:
                item.own_sell_cost_each = (
                    safe_float(item.sell_cost_each)
                    if hasattr(item, "sell_cost_each") and item.sell_cost_each
                    else 0  # If no VLC sell_cost_each, use 0 instead of any calculated base cost
                )

            # Set temporary variable _own_sell_cost_each from the persistent field for calculations
            item._own_sell_cost_each = safe_float(item.own_sell_cost_each)

        # Build a map of parent-child relationships for faster lookup
        children_map = {}
        for item in self.items:
            if item.is_child_part:
                # Determine the parent ID
                parent_id = item.parent_id
                if not parent_id and item.parent_part:
                    # Find the parent item by part_number
                    for p in self.items:
                        if p.part_number == item.parent_part:
                            parent_id = p.name
                            break

                if parent_id:
                    if parent_id not in children_map:
                        children_map[parent_id] = []
                    children_map[parent_id].append(item.name)

        # Helper function to check if an item is a leaf node (has no children)
        # ANY item (whether child or independent) that has no children should get delivery cost
        def is_leaf_node_or_independent(item):
            """
            Check if an item is eligible for delivery cost allocation.
            Returns True for any item (child or independent) that has no children (is a leaf node).
            """
            # Check if this item has any children (regardless of whether it's a child or independent part)
            has_children = (
                item.name in children_map and len(children_map[item.name]) > 0
            )
            # Return True if item has no children (is a leaf node)
            return not has_children

        # Define recursive function to calculate total costs
        def calculate_total_cost(item_name):
            # Find the item by name
            item = next((i for i in self.items if i.name == item_name), None)
            if not item or item._processed:
                return item._total_cost if item else 0

            # Mark as processed to avoid infinite recursion
            item._processed = True

            # Start with the item's own base cost
            total_cost = math.ceil(item._base_cost)

            # Add costs of all children recursively, accounting for their quantities
            children = children_map.get(item.name, [])
            for child_name in children:
                child_item = next((i for i in self.items if i.name == child_name), None)
                if child_item:
                    # First ensure the child's cost is calculated
                    child_base_cost = calculate_total_cost(child_name)
                    # Then add the child's contribution (base cost * quantity) with ceiling rounding
                    child_contribution = math.ceil(
                        child_base_cost * safe_float(child_item.quantity)
                    )
                    total_cost += child_contribution

            # Store the total cost
            item._total_cost = total_cost

            # Update the item's cost fields
            item.each_cost = total_cost

            # For child items, sell_price_each should only include their own cost
            # For parent items, sell_price_each will be calculated later using the correct formula:
            # (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity
            if item.is_child_part:
                # For child items, only include their own cost
                # Round up sell_price_each to whole number (integer)
                item.sell_price_each = math.ceil(
                    math.ceil(item._base_cost)
                    * (1 + (safe_float(item.mark_up_item) / 100))
                )
                # For sell_cost_each, use VLC's sell_cost_each if available, otherwise fallback to base_cost
                if hasattr(item, "sell_cost_each") and item.sell_cost_each:
                    item.sell_cost_each = safe_float(item.sell_cost_each)
            else:
                # For independent items (not child parts and no children), calculate sell_price_each
                # using their own base cost with markup (includes delivery cost if add_to_parts is enabled)
                item.sell_price_each = math.ceil(
                    math.ceil(item._base_cost)
                    * (1 + (safe_float(item.mark_up_item) / 100))
                )
            # Note: Parent items with children will have their sell_price_each calculated later using the correct formula

            # Batch cost is always based on the total cost
            item.batch_cost = safe_float(item.quantity) * total_cost

            # Calculate line_total based on hierarchical structure
            # For all items: line_total is quantity * sell_price_each
            # This ensures all items have a proper line_total
            item.line_total = math.ceil(
                safe_float(item.quantity) * item.sell_price_each
            )

            return total_cost

        # Process all items in bottom-up order (children first, then parents)
        # First, process all leaf nodes (items without children)
        for item in self.items:
            has_children = any(
                child.is_child_part
                and (
                    (not child.parent_id and child.parent_part == item.part_number)
                    or (child.parent_id and child.parent_id == item.name)
                )
                for child in self.items
            )

            if not has_children:
                calculate_total_cost(item.name)

        # Then process items with children, in multiple passes if needed
        unprocessed_items = True
        pass_count = 0
        max_passes = 10  # Safety limit

        while unprocessed_items and pass_count < max_passes:
            pass_count += 1
            unprocessed_items = False

            for item in self.items:
                if not getattr(item, "_processed", False):
                    # Check if all children are processed
                    children = [
                        child
                        for child in self.items
                        if child.is_child_part
                        and (
                            (
                                not child.parent_id
                                and child.parent_part == item.part_number
                            )
                            or (child.parent_id and child.parent_id == item.name)
                        )
                    ]

                    all_children_processed = all(
                        getattr(child, "_processed", False) for child in children
                    )

                    if all_children_processed:
                        calculate_total_cost(item.name)
                    else:
                        unprocessed_items = True

        # Final pass for any remaining items
        for item in self.items:
            if not getattr(item, "_processed", False):
                calculate_total_cost(item.name)

        # STEP 2.5: First calculate each item's own line_total (without children)
        for item in self.items:
            # Calculate each item's own line_total based on its own cost
            item.line_total = safe_float(item.quantity) * (
                math.ceil(item._base_cost) * (1 + (safe_float(item.mark_up_item) / 100))
            )

            # Store the original line_total for hierarchical calculation
            item._own_line_total = item.line_total

        # STEP 2.6: Define a recursive function to calculate hierarchical line_total
        def calculate_hierarchical_line_total(item_id):
            item = next((i for i in self.items if i.name == item_id), None)
            if not item:
                return 0

            # Start with the item's own line_total
            total = safe_float(item._own_line_total)

            # Find all direct children of this item
            children = [
                child
                for child in self.items
                if child.is_child_part
                and (
                    (not child.parent_id and child.parent_part == item.part_number)
                    or (child.parent_id and child.parent_id == item.name)
                )
            ]

            # Add each child's hierarchical line_total (recursively)
            for child in children:
                total += calculate_hierarchical_line_total(child.name)

            # Update the item's line_total with the hierarchical total
            item.line_total = total

            return total

        # STEP 2.7: Apply the recursive calculation to all top-level items
        for item in self.items:
            if not item.is_child_part:
                calculate_hierarchical_line_total(item.name)

        # Ensure delivery_total is calculated at document level before add_to_parts logic
        self.delivery_total = safe_float(self.delivery_cost) * safe_float(
            self.no_of_shipments
        )

        # STEP 2.8: Define hierarchical cost calculation function to be used by both add_to_parts branches
        def run_hierarchical_cost_calculation():
            """Run the complete hierarchical cost calculation including sell_price_each aggregation"""
            # Recalculate costs recursively through the hierarchy in bottom-up order
            # First, process all leaf nodes (items without children)
            for item in self.items:
                has_children = any(
                    child.is_child_part
                    and (
                        (not child.parent_id and child.parent_part == item.part_number)
                        or (child.parent_id and child.parent_id == item.name)
                    )
                    for child in self.items
                )

                if not has_children:
                    calculate_total_cost(item.name)

            # Then process items with children, in multiple passes if needed
            unprocessed_items = True
            pass_count = 0
            max_passes = 10  # Safety limit

            while unprocessed_items and pass_count < max_passes:
                pass_count += 1
                unprocessed_items = False

                for item in self.items:
                    if not getattr(item, "_processed", False):
                        # Check if all children are processed
                        children = [
                            child
                            for child in self.items
                            if child.is_child_part
                            and (
                                (
                                    not child.parent_id
                                    and child.parent_part == item.part_number
                                )
                                or (child.parent_id and child.parent_id == item.name)
                            )
                        ]

                        all_children_processed = all(
                            getattr(child, "_processed", False) for child in children
                        )

                        if all_children_processed:
                            calculate_total_cost(item.name)
                        else:
                            unprocessed_items = True

            # Final pass for any remaining items
            for item in self.items:
                if not getattr(item, "_processed", False):
                    calculate_total_cost(item.name)

            # Calculate line_total using hierarchical structure
            # First calculate each item's own line_total (without children)
            for item in self.items:
                # Calculate each item's own line_total based on its own cost
                item.line_total = safe_float(item.quantity) * (
                    math.ceil(item._base_cost)
                    * (1 + (safe_float(item.mark_up_item) / 100))
                )

                # Store the original line_total for hierarchical calculation
                item._own_line_total = item.line_total

            # Define a recursive function to calculate hierarchical line_total
            def calculate_hierarchical_line_total_inner(item_id):
                item = next((i for i in self.items if i.name == item_id), None)
                if not item:
                    return 0

                # Start with the item's own line_total
                total = safe_float(item._own_line_total)

                # Find all direct children of this item
                children = [
                    child
                    for child in self.items
                    if child.is_child_part
                    and (
                        (not child.parent_id and child.parent_part == item.part_number)
                        or (child.parent_id and child.parent_id == item.name)
                    )
                ]

                # Add each child's hierarchical line_total (recursively)
                for child in children:
                    total += calculate_hierarchical_line_total_inner(child.name)

                # Update the item's line_total with the hierarchical total
                item.line_total = total

                return total

            # Apply the recursive calculation to all top-level items
            for item in self.items:
                if not item.is_child_part:
                    calculate_hierarchical_line_total_inner(item.name)

        # STEP 3: Handle add_to_parts if needed
        if self.add_to_parts:
            # Filter items to only include leaf nodes and independent parts for delivery cost allocation
            eligible_items = [
                item for item in self.items if is_leaf_node_or_independent(item)
            ]

            # Calculate total quantity across eligible items only
            total_qty = sum(safe_float(item.quantity) for item in eligible_items)

            # Calculate delivery cost per unit for eligible items
            delivery_per_unit = (self.delivery_total or 0) / max(total_qty, 1)

            # Process all items, but only allocate delivery costs to eligible ones
            for item in self.items:
                is_eligible = is_leaf_node_or_independent(item)

                # Only allocate delivery costs to leaf nodes and independent parts
                if is_eligible:
                    item.delivery_each = delivery_per_unit
                else:
                    # Parent parts with children should not receive delivery cost allocation
                    item.delivery_each = 0

                # Populate delivery fields from document level to items (for reference)
                item.delivery_type = self.delivery_type or ""
                item.delivery_cost = self.delivery_cost or ""
                item.no_of_shipments = self.no_of_shipments or ""
                item.delivery_total = self.delivery_total or ""

                # Recalculate base cost with the new delivery_each value
                base_cost = sum(safe_float(getattr(item, f)) for f in numeric_fields)

                # Update the item's base cost
                item._base_cost = math.ceil(base_cost)

                # Reset processing flags for recalculation
                item._processed = False

            # STEP 3.2: Run hierarchical cost calculation with delivery costs included
            run_hierarchical_cost_calculation()

            # Recalculate grand total after delivery cost distribution
            # Only include top-level items to avoid double-counting
            updated_grand_total = sum(
                safe_float(item.line_total)
                for item in self.items
                if not item.is_child_part
            )

            self.grand_total = updated_grand_total

            # Set delivery_total to 0 since it's already included in the items
            self.delivery_total = 0

        else:
            # STEP 3.1: Handle delivery when add_to_parts is unchecked
            # Clear all delivery fields from items since delivery should be handled at document level
            for item in self.items:
                # When Add to Parts is unchecked, clear all delivery fields from items
                # The delivery should be handled at document level, not item level
                item.delivery_each = 0
                item.delivery_type = ""
                item.delivery_cost = ""
                item.no_of_shipments = ""
                item.delivery_total = ""

                base_cost = sum(safe_float(getattr(item, f)) for f in numeric_fields)

                # Update the item's base cost
                item._base_cost = math.ceil(base_cost)

                # Reset processing flags for recalculation
                item._processed = False

            # STEP 3.3: Run hierarchical cost calculation for non-add_to_parts case
            run_hierarchical_cost_calculation()

        # STEP 3.4: Calculate grand total based on add_to_parts setting
        if self.add_to_parts:
            # When add_to_parts is enabled, delivery is included in items, so don't add delivery_total
            updated_grand_total = sum(
                safe_float(item.line_total)
                for item in self.items
                if not item.is_child_part
            )
            self.grand_total = updated_grand_total
            # Set delivery_total to 0 since it's already included in the items
            self.delivery_total = 0
        else:
            # When add_to_parts is disabled, add delivery_total to grand_total
            updated_grand_total = sum(
                safe_float(item.line_total)
                for item in self.items
                if not item.is_child_part
            )
            self.grand_total = updated_grand_total + safe_float(self.delivery_total)

        # STEP 4: Calculate document totals normally (when add_to_parts is False)
        if not self.add_to_parts:
            self.delivery_total = safe_float(self.delivery_cost) * safe_float(
                self.no_of_shipments
            )

            # Use VLC's sell_cost_each for raw costs instead of complex recalculation
            # VLC has already calculated the correct raw costs without markup
            for item in self.items:
                # Use VLC's sell_cost_each if available, otherwise fallback to base_cost
                if hasattr(item, "sell_cost_each") and item.sell_cost_each:
                    item._raw_base_cost = safe_float(item.sell_cost_each)
                else:
                    # Fallback to base_cost if no VLC sell_cost_each available
                    item._raw_base_cost = math.ceil(item._base_cost)

            # Calculate operation cost line for each item
            for item in self.items:
                # Store raw base cost as operation cost each
                item._op_cost_each = safe_float(item._raw_base_cost)

                # Calculate operation cost line as quantity * operation cost each
                item._op_cost_line = safe_float(item.quantity) * safe_float(
                    item._op_cost_each
                )

            # Define a recursive function to calculate costs through the hierarchy
            def calculate_child_cost_and_line_total(item_id):
                item = next((i for i in self.items if i.name == item_id), None)
                if not item:
                    return {"child_cost_each": 0, "line_total": 0}

                # Reset the needs_recalculation flag
                if hasattr(item, "needs_recalculation"):
                    item.needs_recalculation = False

                # Find all direct children of this item
                children = [
                    child
                    for child in self.items
                    if child.is_child_part
                    and (
                        (not child.parent_id and child.parent_part == item.part_number)
                        or (child.parent_id and child.parent_id == item.name)
                    )
                ]

                # Calculate child cost each as sum of children's contribution
                child_cost_each = 0

                # Process all children to get their contribution to the parent
                for child in children:
                    # Recursively calculate child's values first
                    calculate_child_cost_and_line_total(child.name)

                    # For each child, calculate its contribution to the parent
                    # This is the child's sell_cost_each * quantity
                    child_contribution = safe_float(child.sell_cost_each) * safe_float(
                        child.quantity
                    )

                    # Add child's contribution to the parent's child cost each
                    child_cost_each += child_contribution

                # Store child cost each for later use
                item._child_cost_each = child_cost_each

                # Calculate raw line total (without markup)
                line_total = safe_float(item._op_cost_line) + safe_float(
                    child_cost_each
                )
                item._line_total_raw = line_total

                # Note: sell_cost_each calculation is handled separately in the main validation logic
                # to avoid duplication and doubling issues. This function only handles line_total calculations.

                return {"child_cost_each": child_cost_each, "line_total": line_total}

            # Apply the calculation to all items
            for item in self.items:
                calculate_child_cost_and_line_total(item.name)

            # Note: Grand total calculation is now handled in STEP 3.4 above

        # STEP 5: Apply the correct parent sell_price_each calculation formula
        # This must be done after all line_totals are calculated
        for item in self.items:
            if not item.is_child_part:  # Only for parent items
                # Find all direct children
                children = [
                    child
                    for child in self.items
                    if child.is_child_part
                    and (
                        (not child.parent_id and child.parent_part == item.part_number)
                        or (child.parent_id and child.parent_id == item.name)
                    )
                ]

                if children:
                    # Apply the correct formula: (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity

                    # Calculate parent's own sell price (base cost with markup)
                    parent_own_sell_price = math.ceil(item._base_cost) * (
                        1 + (safe_float(item.mark_up_item) / 100)
                    )

                    # Calculate parent's own sell price × quantity
                    parent_own_total = parent_own_sell_price * safe_float(item.quantity)

                    # Sum all children's line_totals
                    children_line_total_sum = sum(
                        safe_float(child.line_total) for child in children
                    )

                    # Calculate final sell_price_each using the correct formula
                    total_value = parent_own_total + children_line_total_sum
                    item.sell_price_each = (
                        math.ceil(total_value / safe_float(item.quantity))
                        if safe_float(item.quantity) > 0
                        else 0
                    )

                    # Calculate final sell_cost_each using hierarchical aggregation (no division by quantity)
                    # own_sell_cost_each field contains the original VLC value, _own_sell_cost_each is the temp variable
                    # This approach avoids duplication by using the persistent field as the source

                    # Sum all children's sell_cost_each - same pattern as sell_price_each uses line_total
                    children_cost_total_sum = sum(
                        safe_float(child.sell_cost_each) for child in children
                    )

                    # Final result: parent's own sell_cost_each + children's aggregated costs
                    # Use _own_sell_cost_each (temporary variable) for calculation to avoid duplication
                    # own_sell_cost_each (persistent field) stores the actual parent value
                    item._own_sell_cost_each = safe_float(item.own_sell_cost_each)
                    item.sell_cost_each = (
                        item._own_sell_cost_each + children_cost_total_sum
                    )
