import frappe
from frappe.custom.doctype.property_setter.property_setter import make_property_setter


def execute():
    """Add indexes to Velocetec tables to improve performance."""

    add_indexes_to_velocetec_costing()
    add_indexes_to_velocetec_costing_detail()
    add_indexes_to_velocetec_line_costing()
    add_indexes_to_velocetec_line_routing_detail()
    frappe.db.commit()


def add_index_with_property_setter(doctype, fields, index_name=None):
    """Add index and create property setter for single-field indexes to ensure they survive migrations."""
    try:
        # Add the index using frappe.db.add_index
        frappe.db.add_index(doctype, fields, index_name)

        # For single field indexes, add property setter to ensure they survive migrations
        # Always create property setter even during migrations
        if len(fields) == 1:
            make_property_setter(
                doctype,
                fields[0],
                property="search_index",
                value="1",
                property_type="Check",
                for_doctype=False,  # Applied on docfield
            )
        return True
    except Exception as e:
        frappe.log_error(f"Error adding index {index_name} to {doctype}", str(e))
        return False


def add_indexes_to_velocetec_costing():
    """Add indexes to Velocetec Costing table."""
    doctype = "Velocetec Costing"
    indexes = [
        {"fields": ["reference_name"], "index_name": "reference_name_idx"},
        {"fields": ["reference_doctype"], "index_name": "reference_doctype_idx"},
        {
            "fields": ["quotation_link_status"],
            "index_name": "quotation_link_status_idx",
        },
    ]

    for idx in indexes:
        add_index_with_property_setter(doctype, idx["fields"], idx["index_name"])


def add_indexes_to_velocetec_costing_detail():
    """Add indexes to Velocetec Costing Detail table."""
    doctype = "Velocetec Costing Detail"
    indexes = [
        {"fields": ["part_number"], "index_name": "vcd_part_number_idx"},
        {"fields": ["parent_id"], "index_name": "vcd_parent_id_idx"},
        {"fields": ["parent_part"], "index_name": "vcd_parent_part_idx"},
        {"fields": ["is_child_part"], "index_name": "vcd_is_child_part_idx"},
        {
            "fields": ["parent", "part_number"],
            "index_name": "vcd_parent_part_number_idx",
        },
    ]

    for idx in indexes:
        add_index_with_property_setter(doctype, idx["fields"], idx["index_name"])


def add_indexes_to_velocetec_line_costing():
    """Add indexes to Velocetec Line Costing table."""
    doctype = "Velocetec Line Costing"
    indexes = [
        {"fields": ["velocetec_costing"], "index_name": "vlc_velocetec_costing_idx"},
        {
            "fields": ["velocetec_costing_detail"],
            "index_name": "vlc_velocetec_costing_detail_idx",
        },
        {"fields": ["part_number"], "index_name": "vlc_part_number_idx"},
        {
            "fields": ["velocetec_costing", "velocetec_costing_detail"],
            "index_name": "vlc_vc_vcd_idx",
        },
    ]

    for idx in indexes:
        add_index_with_property_setter(doctype, idx["fields"], idx["index_name"])


def add_indexes_to_velocetec_line_routing_detail():
    """Add indexes to Velocetec Line Routing Detail table."""
    doctype = "Velocetec Line Routing Detail"
    indexes = [
        {"fields": ["workstation_type"], "index_name": "vlrd_workstation_type_idx"},
        {"fields": ["is_fixed"], "index_name": "vlrd_is_fixed_idx"},
        {
            "fields": ["workstation_type", "is_fixed"],
            "index_name": "vlrd_workstation_type_is_fixed_idx",
        },
    ]

    for idx in indexes:
        add_index_with_property_setter(doctype, idx["fields"], idx["index_name"])
