frappe.ui.form.on("Opportunity", {
    refresh: function (frm) {
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__("Velocetec Costing"), function () {
                // Check if a VC already exists for this Opportunity
                frappe.call({
                    method: "frappe.client.get_list",
                    args: {
                        doctype: "Velocetec Costing",
                        filters: { reference_opportunity: frm.doc.name },
                        fields: ["name"],
                        limit_page_length: 1
                    },
                    callback: function (r) {
                        if (r.message && r.message.length > 0) {
                            // VC exists; simply route to it
                            frappe.set_route("Form", "Velocetec Costing", r.message[0].name);
                        } else {
                            // No existing VC; create a new one
                            let new_vc = frappe.model.get_new_doc("Velocetec Costing");
                            new_vc.opportunity = frm.doc.name;

                            if (frm.doc.opportunity_from === "Customer") {
                                new_vc.quotation_to = "Customer";
                                new_vc.customer = frm.doc.party_name;
                                new_vc.customer_primary_contact = frm.doc.contact_person;
                            }

                            if (frm.doc.opportunity_from === "Lead") {
                                new_vc.quotation_to = "Lead";
                                new_vc.lead = frm.doc.party_name;
                            }


                            new_vc.reference_opportunity = frm.doc.name;
                            new_vc.customer_reference = frm.doc.customer_reference;

                            frappe.call({
                                method: "frappe.client.insert",
                                args: { doc: new_vc },
                                callback: function (r) {
                                    if (!r.exc) {
                                        frappe.set_route("Form", "Velocetec Costing", r.message.name);
                                    }
                                }
                            });
                        }
                    }
                });
            }, __("Create"));
        }
    }
});
