frappe.ui.form.on("Opportunity", {
    refresh: function (frm) {
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__("Velocetec Costing"), function () {
                // Check if a VC already exists for this Opportunity
                frappe.call({
                    method: "frappe.client.get_list",
                    args: {
                        doctype: "Velocetec Costing",
                        filters: { reference_opportunity: frm.doc.name },
                        fields: ["name"],
                        limit_page_length: 1
                    },
                    callback: function (r) {
                        if (r.message && r.message.length > 0) {
                            // VC exists; simply route to it
                            frappe.set_route("Form", "Velocetec Costing", r.message[0].name);
                        } else {
                            // No existing VC; create a new one
                            frappe.call({
                                method: "velocetec.api.velocetec_costing.create_velocetec_costing_from_opportunity",
                                args: { opportunity_name: frm.doc.name },
                                callback: function (r) {
                                    if (!r.exc) {
                                        frappe.set_route("Form", "Velocetec Costing", r.message.name);
                                    }
                                }
                            });
                        }
                    }
                });
            }, __("Create"));
        }
    }
});
