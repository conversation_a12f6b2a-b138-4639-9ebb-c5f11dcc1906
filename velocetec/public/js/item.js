// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

frappe.ui.form.on('Item', {
    onload: function (frm) {
        frm.set_query("custom_material_size", function () {
            return {
                filters: {
                    material_form: frm.doc.custom_material_form,
                }
            };
        });
    },

    custom_material_form: function (frm) {
        frm.set_value("custom_material_size", null);
    },

    refresh(frm) {
        frm.add_custom_button(__('Purchase History'), function () {
            if (!frm.doc.item_code) {
                frappe.msgprint('Please save the Item first.');
                return;
            }
            show_purchase_history_dialog(frm.doc.item_code, frm.doc.custom_material_form);
        }, "View");

        // Add Duplicate Item button to View dropdown
        frm.add_custom_button(__('Duplicate Item'), function () {
            if (!frm.doc.item_code) {
                frappe.msgprint('Please save the Item first.');
                return;
            }
            show_duplicate_item_dialog(frm);
        }, "Actions");
    }
});

function show_purchase_history_dialog(item_code, material_form) {
    let d = new frappe.ui.Dialog({
        title: "Purchase History",
        fields: [
            {
                fieldname: "material_size",
                fieldtype: "Link",
                options: "Velocetec Material Size",
                label: "Material Size",
                size: "small",
                get_query: function () {
                    if (material_form) {
                        return {
                            filters: {
                                material_form: material_form
                            }
                        };
                    } else {
                        return {};
                    }
                },
                onchange: function () {
                    let material_size = d.get_value("material_size");
                    refresh_purchase_history_table(d, item_code, material_size);
                }
            },
            {
                fieldtype: "Column Break",
            },
            {
                fieldtype: "Section Break",
            },
            {
                fieldname: "purchase_history_table",
                fieldtype: "Table",
                label: "Purchase History",
                fields: [
                    { fieldname: "purchase_receipt", fieldtype: "Link", options: "Purchase Receipt", label: "Purchase Receipt", in_list_view: 1, columns: 2 },
                    { fieldname: "posting_date", fieldtype: "Date", label: "Date", in_list_view: 1, columns: 2 },
                    { fieldname: "supplier", fieldtype: "Link", options: "Supplier", label: "Supplier", in_list_view: 1, columns: 3 },
                    { fieldname: "rate", fieldtype: "Currency", label: "Rate", in_list_view: 1, columns: 1 },
                    { fieldname: "material_size", fieldtype: "Data", label: "Material Size", in_list_view: 1, columns: 2 }
                ],
                data: []
            }
        ],
        size: "extra-large",
        primary_action_label: "Close",
        primary_action: function () {
            d.hide();
        }
    });

    d.show();
    refresh_purchase_history_table(d, item_code, null);
}

function refresh_purchase_history_table(dialog, item_code, material_size) {
    frappe.call({
        method: "velocetec.api.utils.get_item_receipt_details",
        args: {
            item_code: item_code,
            material_size: material_size || null
        },
        callback: function (r) {
            let table_field = dialog.get_field("purchase_history_table");
            table_field.df.data = r.message || [];
            table_field.refresh();
        }
    });
}

/**
 * Show duplicate item dialog with dimension inputs based on material form
 */
function show_duplicate_item_dialog(frm) {
    const material_form = frm.doc.custom_material_form;

    if (!material_form) {
        frappe.msgprint({
            title: __('Material Form Required'),
            indicator: 'red',
            message: __('Please set the Material Form field before duplicating the item.')
        });
        return;
    }

    // Get base item name by removing dimension suffixes
    const base_item_name = get_base_item_name(frm.doc.item_name || frm.doc.item_code);

    let dialog_fields = [
        {
            fieldtype: 'Data',
            fieldname: 'base_item_name',
            label: 'Base Item Name',
            default: base_item_name,
            read_only: 1
        }
    ];

    // Add dimension fields based on material form
    if (material_form === 'Block') {
        dialog_fields = dialog_fields.concat([
            {
                fieldtype: 'Section Break',
                label: 'Block Dimensions'
            },
            {
                fieldtype: 'Float',
                fieldname: 'x_dim',
                label: 'X Dimension',
                reqd: 1,
                precision: 1
            },
            {
                fieldtype: 'Column Break'
            },
            {
                fieldtype: 'Float',
                fieldname: 'y_dim',
                label: 'Y Dimension',
                reqd: 1,
                precision: 1
            },
            {
                fieldtype: 'Column Break'
            },
            {
                fieldtype: 'Float',
                fieldname: 'z_dim',
                label: 'Z Dimension',
                reqd: 1,
                precision: 1
            }
        ]);
    } else if (material_form === 'Bar') {
        dialog_fields = dialog_fields.concat([
            {
                fieldtype: 'Section Break',
                label: 'Bar Dimensions'
            },
            {
                fieldtype: 'Float',
                fieldname: 'd_dim',
                label: 'D (Diameter)',
                reqd: 1,
                precision: 1
            },
            {
                fieldtype: 'Column Break'
            },
            {
                fieldtype: 'Float',
                fieldname: 'l_dim',
                label: 'L (Length)',
                reqd: 1,
                precision: 1
            }
        ]);
    }

    let dialog = new frappe.ui.Dialog({
        title: __('Duplicate Item with Dimensions'),
        fields: dialog_fields,
        size: 'large',
        primary_action_label: __('Create Duplicate'),
        primary_action: function (values) {
            create_duplicate_item(frm, values, material_form, dialog);
        }
    });

    dialog.show();
}

/**
 * Remove dimension suffixes from item name to get base name
 */
function get_base_item_name(item_name) {
    if (!item_name) return '';

    // Remove dimension patterns like " - 30.0x20.0x10.0" or " - 25.0x100.0"
    return item_name.replace(/\s*-\s*[\d.]+x[\d.]+(?:x[\d.]+)?\s*$/, '').trim();
}

/**
 * Create duplicate item with dimensions
 */
function create_duplicate_item(frm, values, material_form, dialog) {
    // Validate dimensions
    if (material_form === 'Block') {
        if (!values.x_dim || !values.y_dim || !values.z_dim) {
            frappe.msgprint(__('Please enter all block dimensions (X, Y, Z)'));
            return;
        }
    } else if (material_form === 'Bar') {
        if (!values.d_dim || !values.l_dim) {
            frappe.msgprint(__('Please enter all bar dimensions (D, L)'));
            return;
        }
    }

    dialog.hide();

    frappe.call({
        method: 'velocetec.api.item.duplicate_item_with_dimensions',
        args: {
            source_item: frm.doc.name,
            base_item_name: values.base_item_name,
            material_form: material_form,
            dimensions: {
                x_dim: values.x_dim || null,
                y_dim: values.y_dim || null,
                z_dim: values.z_dim || null,
                d_dim: values.d_dim || null,
                l_dim: values.l_dim || null
            }
        },
        callback: function (r) {
            if (r.message && r.message.success) {
                frappe.show_alert({
                    message: __('Item duplicated successfully: {0}', [r.message.new_item_code]),
                    indicator: 'green'
                }, 5);

                // Ask user if they want to open the new item
                frappe.confirm(
                    __('Item "{0}" has been created successfully. Do you want to open it?', [r.message.new_item_code]),
                    function () {
                        frappe.set_route('Form', 'Item', r.message.new_item_code);
                    }
                );
            } else {
                frappe.msgprint({
                    title: __('Error'),
                    indicator: 'red',
                    message: r.message ? r.message.error : __('Failed to duplicate item')
                });
            }
        }
    });
}
