// Copyright (c) 2025, <PERSON>bang<PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on('Purchase Order', {
	onload: function(frm) {
		frm.set_query('material_size', 'items', function (doc, cdt, cdn) {
		    row = locals[cdt][cdn];
            let material_form = null;
            frappe.call({
                method: "frappe.client.get_value",
                args: {
                    doctype: "Item",
                    fieldname: "custom_material_form",
                    filters: { name: row.item_code }
                },
                async: false,
                callback: function (response) {
                    if (response.message) {
                        material_form = response.message.custom_material_form;
                    }
                }
            });
        
            return {
                filters: {
                    material_form: material_form,
                }
            };
        });
	}
});
