frappe.ui.form.on('BOM', {
    refresh: function(frm) {
        // Add button to recalculate costs
        if (!frm.is_new() && frm.doc.docstatus === 0) {
            frm.add_custom_button(__('Recalculate Custom Costs'), function() {
                frappe.call({
                    method: 'velocetec.api.bom.recalculate_bom_custom_costs',
                    args: {
                        bom_name: frm.doc.name
                    },
                    callback: function(r) {
                        if (r.message && r.message.success) {
                            frappe.show_alert({
                                message: __('Costs recalculated successfully'),
                                indicator: 'green'
                            });
                            frm.reload_doc();
                        } else {
                            frappe.show_alert({
                                message: __('No custom costs found or error occurred'),
                                indicator: 'orange'
                            });
                        }
                    }
                });
            }, __('Utilities'));
        }
    }
});
