import frappe
from frappe.utils import flt
from erpnext.manufacturing.doctype.bom.bom import BOM

original_calculate_cost = BOM.calculate_cost
original_calculate_op_cost = BOM.calculate_op_cost


def patched_calculate_op_cost(self, update_hour_rate=False):
    """
    Override the calculate_op_cost method to ensure operation costs are calculated correctly.
    """
    original_calculate_op_cost(self, update_hour_rate)

    if not self.get("bom_creator"):
        return

    if hasattr(self, "flags") and self.flags.get("skip_calculate_cost"):
        return

    operating_cost = 0
    base_operating_cost = 0

    if self.get("with_operations") and self.get("operations"):
        for d in self.get("operations"):
            if d.get("operating_cost"):
                operating_cost += flt(d.operating_cost)
                base_operating_cost += flt(d.base_operating_cost or d.operating_cost)

    if operating_cost > 0 and self.operating_cost != operating_cost:
        self.operating_cost = operating_cost
        self.base_operating_cost = base_operating_cost

        frappe.logger().info(
            f"Updated operation costs for BOM {self.name} (from BOM Creator): {operating_cost}"
        )


def patched_calculate_cost(self, save_updates=False, update_hour_rate=False):
    """
    Override the calculate_cost method to respect custom costs.
    """
    if not self.get("bom_creator"):
        return original_calculate_cost(self, save_updates, update_hour_rate)

    if hasattr(self, "flags") and self.flags.get("skip_calculate_cost"):
        frappe.logger().info(
            f"Skipping cost calculation for BOM {self.name} (from BOM Creator) due to custom costs"
        )
        return

    has_custom_costs = False
    custom_cost_total = 0

    for item in self.items:
        if hasattr(item, "custom_cost") and flt(item.custom_cost) > 0:
            has_custom_costs = True
            custom_cost_total += flt(item.custom_cost) * flt(item.qty)

    if has_custom_costs and custom_cost_total > 0:
        self.calculate_op_cost(update_hour_rate)

        self.raw_material_cost = custom_cost_total
        self.base_raw_material_cost = custom_cost_total

        self.total_cost = (
            flt(self.operating_cost)
            + flt(custom_cost_total)
            - flt(self.scrap_material_cost)
        )
        self.base_total_cost = (
            flt(self.base_operating_cost)
            + flt(custom_cost_total)
            - flt(self.base_scrap_material_cost)
        )

        self.flags.skip_calculate_cost = True

        frappe.logger().info(
            f"Using custom costs for BOM {self.name} (from BOM Creator): {custom_cost_total}"
        )
        return

    return original_calculate_cost(self, save_updates, update_hour_rate)


# Monkey patch the BOM class
BOM.calculate_op_cost = patched_calculate_op_cost
BOM.calculate_cost = patched_calculate_cost
