import frappe
from frappe import _
from frappe.utils import flt


@frappe.whitelist()
def recalculate_bom_custom_costs(bom_name):
    """
    Manually recalculate BOM costs based on custom_cost values.
    """
    if not bom_name:
        return {"success": False}

    result = update_bom_costs_directly(bom_name)
    return {"success": result}


@frappe.whitelist()
def recalculate_bom_costs(doc, method=None):
    """
    Recalculate BOM costs based on custom_cost values.
    """
    if not doc:
        return

    # Handle BOM Item
    if doc.doctype == "BOM Item" and doc.parent:
        update_bom_costs_directly(doc.parent)
        return

    # Handle BOM
    if doc.doctype == "BOM" and doc.docstatus != 1:
        update_bom_costs_directly(doc.name)


def update_bom_costs_directly(bom_name):
    """
    Update BOM costs based on custom_cost values.
    """
    try:
        # Skip if BOM is submitted
        if frappe.db.get_value("BOM", bom_name, "docstatus") == 1:
            return False

        # Get BOM items with custom costs
        items = frappe.db.get_all(
            "BOM Item",
            filters={"parent": bom_name, "custom_cost": [">", 0]},
            fields=["item_code", "qty", "custom_cost"],
        )

        # Calculate total custom cost
        custom_cost_total = sum(
            float(item.custom_cost) * float(item.qty) for item in items
        )
        if not custom_cost_total:
            return False

        # Get BOM values
        bom = frappe.db.get_value(
            "BOM",
            bom_name,
            [
                "operating_cost",
                "scrap_material_cost",
                "base_operating_cost",
                "base_scrap_material_cost",
            ],
            as_dict=1,
        )

        # Calculate total costs
        operating_cost = float(bom.operating_cost or 0)
        scrap_material_cost = float(bom.scrap_material_cost or 0)
        base_operating_cost = float(bom.base_operating_cost or 0)
        base_scrap_material_cost = float(bom.base_scrap_material_cost or 0)

        total_cost = operating_cost + custom_cost_total + scrap_material_cost
        base_total_cost = (
            base_operating_cost + custom_cost_total + base_scrap_material_cost
        )

        # Update BOM
        frappe.db.set_value(
            "BOM",
            bom_name,
            {
                "raw_material_cost": custom_cost_total,
                "base_raw_material_cost": custom_cost_total,
                "total_cost": total_cost,
                "base_total_cost": base_total_cost,
            },
        )

        return True
    except Exception:
        return False


def calculate_operation_costs(doc):
    """
    Calculate operation costs for the BOM.
    """
    if not doc.get("bom_creator"):
        return False

    operating_cost = 0
    base_operating_cost = 0

    if doc.get("with_operations") and doc.get("operations"):
        for d in doc.get("operations"):
            if d.get("operating_cost"):
                operating_cost += flt(d.operating_cost)
                base_operating_cost += flt(d.base_operating_cost or d.operating_cost)

    if operating_cost > 0:
        doc.operating_cost = operating_cost
        doc.base_operating_cost = base_operating_cost

        frappe.logger().info(
            f"Updated operation costs for BOM {doc.name} (from BOM Creator): {operating_cost}"
        )

    scrap_cost = 0
    base_scrap_cost = 0

    if doc.get("scrap_items"):
        for d in doc.get("scrap_items"):
            scrap_cost += flt(d.amount)
            base_scrap_cost += flt(d.base_amount or d.amount)

    if scrap_cost > 0:
        doc.scrap_material_cost = scrap_cost
        doc.base_scrap_material_cost = base_scrap_cost

        frappe.logger().info(
            f"Updated scrap costs for BOM {doc.name} (from BOM Creator): {scrap_cost}"
        )

    return True


def override_bom_costs(doc, method=None):
    """
    Override BOM costs with custom costs.
    This function runs before the BOM is saved.
    """
    if not doc.get("bom_creator"):
        return False

    if doc.docstatus != 0 and doc.docstatus != 1:  # Only for draft or submitted BOMs
        return False

    calculate_operation_costs(doc)

    has_custom_costs = False
    custom_cost_total = 0

    for item in doc.items:
        if hasattr(item, "custom_cost") and flt(item.custom_cost) > 0:
            has_custom_costs = True
            custom_cost_total += flt(item.custom_cost) * flt(item.qty)

    if has_custom_costs and custom_cost_total > 0:
        frappe.logger().info(
            f"Using custom costs for BOM {doc.name} (from BOM Creator): {custom_cost_total}"
        )

        doc.raw_material_cost = custom_cost_total
        doc.base_raw_material_cost = custom_cost_total

        doc.total_cost = (
            flt(doc.operating_cost)
            + flt(custom_cost_total)
            - flt(doc.scrap_material_cost)
        )
        doc.base_total_cost = (
            flt(doc.base_operating_cost)
            + flt(custom_cost_total)
            - flt(doc.base_scrap_material_cost)
        )

        doc.flags.ignore_calculate_cost = True

        return True

    return False


def skip_cost_calculation(doc, method=None):
    """
    Skip the standard cost calculation if we've already set custom costs.
    This function runs before the BOM's calculate_cost method.
    """
    if not doc.get("bom_creator"):
        return False

    if hasattr(doc, "flags") and doc.flags.get("ignore_calculate_cost"):
        doc.flags.skip_calculate_cost = True
        frappe.logger().info(
            f"Setting skip_calculate_cost flag for BOM {doc.name} (from BOM Creator)"
        )
        return True

    return False


@frappe.whitelist()
def populate_operations_on_bom(doc, method=None):
    # Only proceed if this BOM was created via BOM Creator
    if doc.get("bom_creator"):
        # Fix BOM quantity normalization issue
        # Child BOMs (with bom_creator_item) should have quantity = 1
        if doc.get("bom_creator_item") and flt(doc.quantity) > 1:
            doc.quantity = 1

        # Continue with normal operations
        if doc.bom_creator_item:
            vlc_id = frappe.db.get_value(
                "BOM Creator Item",
                doc.bom_creator_item,
                "custom_velocetec_line_costing",
            )
        else:
            vlc_id = frappe.db.get_value(
                "BOM Creator", doc.bom_creator, "custom_velocetec_line_costing"
            )

        bom_creator = frappe.get_doc("BOM Creator", doc.bom_creator)

        if vlc_id:
            doc.custom_velocetec_line_costing = vlc_id
            doc.custom_sales_order = bom_creator.custom_sales_order
            vlc = frappe.get_doc("Velocetec Line Costing", vlc_id)
            routing_details = vlc.get("routing_material_details") or []
            # Iterate over routing_material_details from the VLC
            doc.operations = []
            for rm in routing_details:
                op_row = doc.append("operations", {})
                op_row.sequence_id = rm.get("idx")
                op_row.operation = rm.get("operation")
                op_row.workstation_type = rm.get("workstation_type")
                op_row.time_in_mins = (rm.get("hrs") or 0) * 60
                op_row.hour_rate = rm.get("rate_hr")
                op_row.fixed_time = rm.get("is_fixed")
                op_row.operating_cost = rm.get("cost_each")
                op_row.batch_size = 1

            if doc.operations:
                doc.with_operations = 1

            for bom_creator_item in bom_creator.items:
                if bom_creator_item.custom_cost:
                    for bom_item in doc.items:
                        if bom_creator_item.item_code == bom_item.item_code:
                            bom_item.custom_cost = bom_creator_item.custom_cost
                            break

                if bom_creator_item.custom_material_size:
                    for bom_item in doc.items:
                        if bom_creator_item.item_code == bom_item.item_code:
                            bom_item.custom_material_size = (
                                bom_creator_item.custom_material_size
                            )
                            break

        else:
            for bom_creator_item in bom_creator.items:
                if bom_creator_item.custom_cost:
                    for bom_item in doc.items:
                        if bom_creator_item.item_code == bom_item.item_code:
                            bom_item.custom_cost = bom_creator_item.custom_cost
                            break

                if bom_creator_item.custom_material_size:
                    for bom_item in doc.items:
                        if bom_creator_item.item_code == bom_item.item_code:
                            bom_item.custom_material_size = (
                                bom_creator_item.custom_material_size
                            )
                            break
