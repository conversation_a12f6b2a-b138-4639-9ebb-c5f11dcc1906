import frappe
import json
import re
import math
from frappe.model.mapper import get_mapped_doc


@frappe.whitelist()
def re_calculate_vlc_logic(docname, qty, method=None):
    """Re-validate the Velocetec Line Costing document for Velocetec Costing Detail."""

    doc = frappe.get_doc("Velocetec Line Costing", docname)
    quotation_link_status = frappe.db.get_value(
        "Velocetec Costing", doc.velocetec_costing, "quotation_link_status"
    )
    if quotation_link_status == "Linked":
        return

    doc.local_qty = qty
    doc.validate()

    # Update the parent VCD with the updated VLC data
    if doc.velocetec_costing_detail:
        frappe.db.set_value(
            "Velocetec Costing Detail",
            doc.velocetec_costing_detail,
            "velocetec_line_costing",
            json.dumps(doc.as_dict(no_nulls=1), default=str),
        )

    frappe.response["velocetec_line_costing"] = json.dumps(
        doc.as_dict(no_nulls=1), default=str
    )


@frappe.whitelist()
def update_vlc_on_quantity_change(vc_detail_id, new_quantity):
    """Update the VLC when quantity changes in VC."""
    try:
        # Check if this VCD has a linked VLC
        vlc_name = frappe.db.get_value(
            "Velocetec Line Costing", {"velocetec_costing_detail": vc_detail_id}, "name"
        )

        if not vlc_name:
            return {"success": False, "message": "No VLC found for this detail"}

        # Get the VCD to check if it's part of a linked VC
        vcd = frappe.get_doc("Velocetec Costing Detail", vc_detail_id)
        vc_name = vcd.parent

        # Check if the parent VC is linked to a quotation
        quotation_link_status = frappe.db.get_value(
            "Velocetec Costing", vc_name, "quotation_link_status"
        )
        if quotation_link_status == "Linked":
            return {
                "success": False,
                "message": "This VC is linked to a quotation; updates are disabled",
            }

        # Get the VLC document
        vlc_doc = frappe.get_doc("Velocetec Line Costing", vlc_name)

        # Update the local_qty and run validation
        vlc_doc.local_qty = float(new_quantity)
        vlc_doc.validate()

        # Get the updated VLC data
        vlc_data = vlc_doc.as_dict(no_nulls=1)

        # Calculate raw_total_cost using the same logic as in VLC validation
        # This ensures we get the correct value that includes special handling for Inspection and Mass Finishing
        try:
            # Get raw material costs (without markup)
            # Note: We need to calculate raw material costs from the individual rows
            # since block_material_amount and bar_material_amount now store price values (with markup)
            raw_material_cost = sum(
                row.amount or 0
                for row in vlc_doc.line_material_details or []
                if row.x and row.y and row.z
            ) + sum(
                row.amount or 0
                for row in vlc_doc.bar_material_details or []
                if row.d and row.l
            )

            # Get raw fixings cost (without markup)
            raw_fixings_cost = 0
            for row in vlc_doc.line_fixings or []:
                raw_fixings_cost += (row.qty or 0) * (row.costunit or 0)

            # Get raw workstation costs - these already have the special handling applied
            # For Inspection and Mass Finishing, the VLC validation method applies the special logic
            # We need to use the raw costs that were calculated in the VLC validation
            # These are stored in the raw_inspection and raw_mass_finishing variables

            # Since we don't have direct access to those variables, we'll use the values from the VLC document
            # which already have the special handling applied
            raw_total_cost = sum(
                [
                    raw_material_cost,
                    vlc_doc.machining or 0,
                    vlc_doc.design or 0,
                    vlc_doc.finishing or 0,
                    vlc_doc.inspection
                    or 0,  # This already has the special inspection qty handling
                    vlc_doc.other or 0,
                    vlc_doc.mass_finishing
                    or 0,  # This already has the special mass finishing loads handling
                    vlc_doc.sub_con or 0,
                    raw_fixings_cost,
                    vlc_doc.tool_making or 0,
                    vlc_doc.turning or 0,
                    vlc_doc.edm or 0,
                    vlc_doc.assembly or 0,
                ]
            )
        except Exception as e:
            frappe.log_error(
                message=f"Error calculating raw_total_cost: {str(e)}",
                title="VLC Quantity Update Error",
            )
            # Fallback to simple sum if there's an error
            # Calculate raw material costs from individual rows
            raw_block_material = sum(
                row.amount or 0
                for row in vlc_doc.line_material_details or []
                if row.x and row.y and row.z
            )
            raw_bar_material = sum(
                row.amount or 0
                for row in vlc_doc.bar_material_details or []
                if row.d and row.l
            )
            raw_fixings = sum(
                (row.qty or 0) * (row.costunit or 0)
                for row in vlc_doc.line_fixings or []
            )

            raw_total_cost = sum(
                [
                    raw_block_material,
                    raw_bar_material,
                    vlc_doc.machining or 0,
                    vlc_doc.design or 0,
                    vlc_doc.finishing or 0,
                    vlc_doc.inspection or 0,
                    vlc_doc.other or 0,
                    vlc_doc.mass_finishing or 0,
                    vlc_doc.sub_con or 0,
                    raw_fixings,
                    vlc_doc.tool_making or 0,
                    vlc_doc.turning or 0,
                    vlc_doc.edm or 0,
                    vlc_doc.assembly or 0,
                ]
            )

        # Update the VCD with the new quantity, VLC data, and cost fields
        frappe.db.set_value(
            "Velocetec Costing Detail",
            vc_detail_id,
            {
                "quantity": float(new_quantity),  # Explicitly update the quantity field
                "velocetec_line_costing": json.dumps(vlc_data, default=str),
                "material_price": vlc_doc.material_price or 0,
                "machining": vlc_doc.machining or 0,
                "finishing": vlc_doc.finishing or 0,
                "inspection": vlc_doc.inspection or 0,
                "mass_finishing": vlc_doc.mass_finishing or 0,
                "other": vlc_doc.other or 0,
                "sub_con": vlc_doc.sub_con or 0,
                "tool_making": vlc_doc.tool_making or 0,
                "turning": vlc_doc.turning or 0,
                "design": vlc_doc.design or 0,
                "edm": vlc_doc.edm or 0,
                "assembly": vlc_doc.assembly or 0,
                "fixings": vlc_doc.total_fixings_cost or 0,
                "sell_cost_each": raw_total_cost,  # Explicitly update sell_cost_each
            },
        )

        # Force a recalculation of the parent VC
        vc_doc = frappe.get_doc("Velocetec Costing", vc_name)
        vc_doc.run_method("validate")
        frappe.db.commit()

        return {
            "success": True,
            "message": "VLC and VC updated successfully",
            "velocetec_line_costing": json.dumps(vlc_data, default=str),
            "vc_name": vc_name,
        }
    except Exception as e:
        frappe.log_error(
            message=f"Error updating VLC for {vc_detail_id} with quantity {new_quantity}: {str(e)}",
            title="VLC Quantity Update Error",
        )
        return {"success": False, "message": str(e)}


@frappe.whitelist()
def create_quotation_from_selection(vc_docname, selected_parents, selected_children):
    """
    Create a Quotation from the selected items in the Velocetec Costing.
    Implements mutually exclusive parent/child mode behavior:
    - Parent mode: Show parent parts with full cost (includes child costs) + child part numbers only (no child descriptions/costs)
    - Child mode: Show ALL parts individually with their own selling costs (own_sell_cost_each) + delivery distribution
      This pulls everything in the VC to the Quotation with individual selling prices and proper delivery_total distribution
    Note: Only two modes are supported - parent or child, not both simultaneously
    """

    # Convert JSON strings to Python lists
    selected_parents = json.loads(selected_parents)
    selected_children = json.loads(selected_children)

    # Fetch the VC document
    vc_doc = frappe.get_doc("Velocetec Costing", vc_docname)

    # Check if a Quotation is already linked
    if vc_doc.reference_doctype == "Quotation" and vc_doc.reference_name:
        frappe.throw(
            f"A Quotation ({vc_doc.reference_name}) is already linked to this Velocetec Costing document."
        )

    # Ensure each part number is an Item
    def ensure_item(item_code, internal_notes=None, external_notes=None):
        item = frappe.db.get_value(
            "Item",
            item_code,
            ["name", "item_name", "stock_uom", "standard_rate"],
            as_dict=True,
        )
        if not item:
            new_item = frappe.get_doc(
                {
                    "doctype": "Item",
                    "item_code": item_code,
                    "item_name": item_code,
                    "item_group": "Custom Manufactured Parts",
                    "uom": "Nos",
                    "is_sales_item": 1,
                    "is_stock_item": 1,
                    "custom_internal_notes": internal_notes,
                    "custom_external_notes": external_notes,
                    "has_batch_no": 1,
                    "has_serial_no": 1,
                }
            )
            new_item.insert(ignore_permissions=True, ignore_mandatory=True)
            frappe.msgprint(f"Item {item_code} created.")
            frappe.msgprint(str(new_item))
            return new_item
        return item

    # Ensure all VC items exist as Items
    for row in vc_doc.items:
        if row.part_number:
            ensure_item(row.part_number, row.internal_notes, row.external_notes)

    # --- Build Quotation Items with Proper Toggle Logic ---
    quotation_items = []

    # Determine the toggle behavior based on selections
    has_selected_parents = bool(selected_parents)
    has_selected_children = bool(selected_children)

    # Build a map of parent-child relationships for reference
    parent_child_map = {}
    for row in vc_doc.items:
        if row.is_child_part:
            parent_row = None

            # First try to find parent by parent_id
            if row.parent_id:
                parent_row = next(
                    (p for p in vc_doc.items if p.name == row.parent_id), None
                )

            # If no parent_id or parent not found, try to find by parent_part
            if not parent_row and row.parent_part:
                parent_row = next(
                    (p for p in vc_doc.items if p.part_number == row.parent_part), None
                )

            # Add to parent_child_map if parent found
            if parent_row and parent_row.part_number:
                if parent_row.part_number not in parent_child_map:
                    parent_child_map[parent_row.part_number] = []
                parent_child_map[parent_row.part_number].append(row)

    # Helper function to add quotation item
    def add_quotation_item(row, rate, description_suffix="", custom_description=None):
        """Add a quotation item with consistent formatting"""
        if not row.part_number:
            frappe.throw(f"Part number missing for row idx {row.idx}")

        # Use custom description if provided, otherwise use row description
        if custom_description is not None:
            description = custom_description
        else:
            description = row.description or ""
            if description_suffix:
                description += description_suffix

        if row.lead_time_days:
            row.delivery_date = None

        return {
            "item_code": row.part_number,
            "item_name": row.part_number,
            "qty": row.quantity,
            "uom": "Nos",
            "rate": rate,
            "price_list_rate": rate,
            "description": description,
            "delivery_date": row.delivery_date,
            "lead_time_days": row.lead_time_days,
            "custom_internal_notes": row.internal_notes,
            "custom_external_notes": row.external_notes,
        }

    # Helper function to process items in parent-first order with recursive hierarchy support
    def process_items_in_order(parent_processor, child_processor):
        """Process parent items first, then their children recursively in hierarchical order"""

        def process_children_recursively(parent_part_number, parent_item):
            """Recursively process all children and grand-children of a parent"""
            children = parent_child_map.get(parent_part_number, [])
            for child in children:
                child_result = child_processor(child, parent_item)
                if child_result:
                    quotation_items.append(child_result)

                # Recursively process this child's children (grand-children)
                if child.part_number:
                    process_children_recursively(child.part_number, child)

        # First, process all top-level parent items (non-child parts)
        for row in vc_doc.items:
            if not row.is_child_part:
                result = parent_processor(row)
                if result:
                    quotation_items.append(result)

                # Then recursively process all children and grand-children of this parent
                if row.part_number:
                    process_children_recursively(row.part_number, row)

    if has_selected_parents and not has_selected_children:
        # Parent only: Show parent parts with full cost (includes child costs) + child part numbers only
        # This avoids double-counting by showing only parent costs which already include child costs

        def parent_processor(row):
            """Process parent items with full cost"""
            if row.part_number in selected_parents:
                description = row.description or ""

                # Get all descendant part numbers recursively
                def get_all_descendant_part_numbers(parent_part_number):
                    """Recursively get all descendant part numbers"""
                    descendants = []
                    children = parent_child_map.get(parent_part_number, [])
                    for child in children:
                        if child.part_number:
                            descendants.append(child.part_number)
                            # Recursively get grand-children
                            descendants.extend(
                                get_all_descendant_part_numbers(child.part_number)
                            )
                    return descendants

                all_descendant_part_numbers = get_all_descendant_part_numbers(
                    row.part_number
                )

                if all_descendant_part_numbers:
                    # Format description as requested: "Parent Description\nConsisting of:\nChild 1 part number\nChild 2 part number"
                    child_list = "<br>".join(all_descendant_part_numbers)
                    # Handle empty parent description properly
                    if description.strip():
                        description = f"{description}<br><strong>Consisting of:</strong><br>{child_list}"
                    else:
                        description = f"<strong>Consisting of:</strong><br>{child_list}"

                return add_quotation_item(
                    row, row.sell_price_each, "", custom_description=description
                )
            return None

        def child_processor(child, parent):
            """No child processing needed for parent-only mode"""
            return None

        # Use parent-first ordering
        process_items_in_order(parent_processor, child_processor)

    elif has_selected_children:
        # Child mode: Show ALL parts individually with their own costs
        # This implements the requirement to "pull everything in the VC to the Quotation with their own selling each"
        # Note: This handles both "child only" and "both parent and child" cases as child mode

        # Helper function to check if an item is a leaf node (has no children)
        # ANY item (whether child or independent) that has no children should get delivery cost
        def is_leaf_node_or_independent(item):
            # Check if this item has any children (regardless of whether it's a child or independent part)
            has_children = any(
                child.is_child_part
                and (
                    (child.parent_id and child.parent_id == item.name)
                    or (not child.parent_id and child.parent_part == item.part_number)
                )
                for child in vc_doc.items
            )
            # Return true if item has no children (is a leaf node)
            return not has_children

        # Calculate delivery distribution for child mode
        # Only distribute delivery costs to leaf nodes and independent parts (same as VC logic)
        eligible_items = [
            item
            for item in vc_doc.items
            if is_leaf_node_or_independent(item) and item.part_number
        ]

        # Calculate total quantity across eligible items only
        total_qty = sum(float(item.quantity or 0) for item in eligible_items)

        # Calculate delivery cost per unit for eligible items
        delivery_per_unit = (
            float(vc_doc.delivery_total or 0) / (total_qty or 1)
            if vc_doc.add_to_parts
            else 0
        )

        # Create a map to store delivery_each values for each part number
        delivery_each_map = {}
        for item in vc_doc.items:
            if is_leaf_node_or_independent(item) and item.part_number:
                delivery_each_map[item.part_number] = delivery_per_unit
            else:
                delivery_each_map[item.part_number] = 0

        def parent_processor(row):
            """Process ALL parent items with their own individual costs"""
            if not row.is_child_part and row.part_number:
                # In Child Mode, use add_to_parts_sell_cost_each (individual cost without hierarchical aggregation)
                own_price = getattr(row, "add_to_parts_sell_cost_each", 0) or 0

                # Add delivery cost to the rate if this is a leaf node or independent part
                # and add_to_parts is enabled
                if vc_doc.add_to_parts and row.part_number in delivery_each_map:
                    own_price += delivery_each_map[row.part_number]
                own_price = math.ceil(own_price)

                return add_quotation_item(row, own_price, "")
            return None

        def child_processor(child, parent):
            """Process ALL child items with their individual costs"""
            if child.is_child_part and child.part_number:
                # In Child Mode, use add_to_parts_sell_cost_each (individual cost without hierarchical aggregation)
                own_price = getattr(child, "add_to_parts_sell_cost_each", 0) or 0

                # Add delivery cost to the rate if this is a leaf node or independent part
                # and add_to_parts is enabled
                if vc_doc.add_to_parts and child.part_number in delivery_each_map:
                    own_price += delivery_each_map[child.part_number]

                own_price = math.ceil(own_price)

                return add_quotation_item(child, own_price, "")
            return None

        # Use parent-first ordering to process ALL items
        process_items_in_order(parent_processor, child_processor)

    if not quotation_items:
        frappe.throw("No items available to create a Quotation.")

    # Quotation Versioning Logic
    def get_next_version(existing_versions, base):
        """
        If no existing_versions, return base.
        Otherwise, look at the last entry in existing_versions:
        - We find the last '-' using rfind.
        - If none, last_version + '-A'
        - If there's a suffix:
            if single alpha -> increment, else fallback to '-A'.
        """
        if not existing_versions:
            return base

        last_version = existing_versions[-1]
        dash_pos = last_version.rfind("-")
        if dash_pos == -1:
            # No dash => produce something like 'Q25-0156-A'
            return f"{last_version}-A"
        else:
            base_part = last_version[:dash_pos]
            suffix = last_version[dash_pos + 1 :]
            # If suffix is single letter, increment it
            if len(suffix) == 1 and suffix.isalpha():
                next_letter = chr(ord(suffix.upper()) + 1)
                return f"{base_part}-{next_letter}"
            else:
                # fallback
                return f"{last_version}-A"

    existing_versions = vc_doc.get("quotation_version") or []
    if isinstance(existing_versions, str):
        existing_versions = json.loads(existing_versions)

    # If no versions, let system generate name for the first Quotation
    if not existing_versions:
        new_quotation_version = None
    else:
        base_quotation_number = existing_versions[0]
        new_quotation_version = get_next_version(
            existing_versions, base_quotation_number
        )

    # Create Quotation with dict -> new_doc -> insert
    def postprocess(source, target):
        target.order_type = "Sales"
        target.selling_price_list = "Standard Selling"
        target.transaction_date = frappe.utils.nowdate()
        target.delivery_type = source.get("delivery_type")

        # Add quotation items
        for item in quotation_items:
            target.append("items", item)

        # Set party_name based on customer or lead
        if source.get("customer"):
            target.party_name = source.get("customer")
        elif source.get("lead"):
            target.party_name = source.get("lead")

    quotation = get_mapped_doc(
        "Velocetec Costing",
        vc_doc.name,
        {
            "Velocetec Costing": {
                "doctype": "Quotation",
                "field_map": {
                    "quotation_to": "quotation_to",
                    "name": "velocetec_costing"
                },
            }
        },
        postprocess=postprocess
    )
    quotation.insert(ignore_permissions=True)  # Insert first

    if vc_doc.get("delivery_type") and not vc_doc.get("add_to_parts"):
        quotation.shipping_rule = vc_doc.get("delivery_type")

        total_delivery_cost = vc_doc.get("delivery_total", 0)

        if total_delivery_cost > 0:
            shipping_rule = frappe.get_doc("Shipping Rule", vc_doc.get("delivery_type"))
            default_shipping_amount = frappe.utils.flt(shipping_rule.shipping_amount, 0)

            if total_delivery_cost != default_shipping_amount:
                quotation.append(
                    "taxes",
                    {
                        "charge_type": "Actual",
                        "account_head": shipping_rule.account,
                        "cost_center": shipping_rule.cost_center,
                        "description": f"{shipping_rule.label} (VC Amount)",
                        "tax_amount": total_delivery_cost,
                        "included_in_print_rate": 0,
                        "included_in_paid_amount": 0,
                    },
                )

                quotation.shipping_rule = ""

        quotation.save(ignore_permissions=True)

    # If it's the first quotation, store the name as the base
    if not new_quotation_version:
        new_quotation_version = quotation.name
    else:
        # For subsequent quotations, rename to our computed version
        old_name = quotation.name
        desired_name = new_quotation_version

        if old_name != desired_name:
            frappe.rename_doc(
                "Quotation", old_name, desired_name, show_alert=False, force=True
            )
            quotation = frappe.get_doc("Quotation", desired_name)

    # Update custom_quotation_version if it exists
    quotation.custom_quotation_version = new_quotation_version
    quotation.save(ignore_permissions=True)

    # Link the Quotation to the VC
    frappe.db.set_value(
        "Velocetec Costing", vc_docname, "reference_doctype", "Quotation"
    )
    frappe.db.set_value(
        "Velocetec Costing", vc_docname, "reference_name", quotation.name
    )

    # Append the final name to quotation_version
    existing_versions.append(quotation.name)
    frappe.db.set_value(
        "Velocetec Costing",
        vc_docname,
        {
            "quotation_version": json.dumps(existing_versions),
            "quotation_link_status": "Linked",
        },
    )

    # Add comment to track quotation linking
    vc_doc = frappe.get_doc("Velocetec Costing", vc_docname)
    vc_doc.add_comment(
        "Info",
        f"Quotation {quotation.name} linked by {frappe.session.user}",
        comment_email=frappe.session.user,
    )

    # Return success
    frappe.response["message"] = {
        "message": "Quotation created successfully.",
        "quotation": quotation.name,
        "reload_doc": True,
    }


@frappe.whitelist()
def duplicate_costing_tree_node(detail_name, duplicate_from_child=False):
    """
    Duplicate a node in the Velocetec Costing tree.
    """

    # Define which fields to copy from the original doc.
    keep_fields = [
        "quantity",
        "delivery_date",
        "mark_up_item",
        "delivery_type",
        "delivery_cost",
        "delivery_total",
        "notes",
    ]

    # Fetch the original Velocetec Costing Detail doc.
    original = frappe.get_doc("Velocetec Costing Detail", detail_name)

    # Create a new child entry (not a new standalone doc).
    new_child = {
        # Initialize basic fields to avoid None values
        "material_price": 0,
        "machining": 0,
        "design": 0,
        "finishing": 0,
        "inspection": 0,
        "other": 0,
        "mass_finishing": 0,
        "sub_con": 0,
        "fixings": 0,
        "tool_making": 0,
        "turning": 0,
        "edm": 0,
        "assembly": 0,
        "delivery_each": 0,
        "batch_cost": 0,
        "each_cost": 0,
        "sell_price_each": 0,
        "sell_cost_each": 0,
        "line_total": 0,
    }

    # Copy selected fields from the original
    for df in original.meta.fields:
        if df.fieldname in keep_fields:
            new_child[df.fieldname] = original.get(df.fieldname)

    # Copy additional fields or modify as needed
    new_child["description"] = original.description
    new_child["dup_reference_part_number"] = original.part_number
    new_child["dup_reference_part_number_name"] = original.name
    new_child["part_number"] = "*copy*" + (original.part_number or "")
    new_child["is_child_part"] = original.is_child_part
    new_child["skip_duplicate"] = 1
    new_child["is_dup_reference_created"] = (
        False  # Ensure this is set to False for proper VLC creation
    )

    # Check if the original has a VLC reference and mark it for duplication
    if frappe.db.exists(
        "Velocetec Line Costing", {"velocetec_costing_detail": original.name}
    ):
        new_child["has_vlc_reference"] = 1
        # DO NOT copy the VLC reference - let the validate method create a new one
        # This prevents double VLC creation
    else:
        new_child["has_vlc_reference"] = 0

    # Handle parent-child relationships
    if duplicate_from_child:
        # If duplicating a child, maintain its parent relationships
        new_child["parent_id"] = original.parent_id
        new_child["parent_part"] = original.parent_part
    else:
        # If duplicating a parent, ensure it's not marked as a child of anything
        if not original.is_child_part:
            new_child["parent_id"] = ""
            new_child["parent_part"] = ""

    # Append to parent’s 'items' child table
    parent_doc = frappe.get_doc("Velocetec Costing", original.parent)
    parent_doc.append("items", new_child)

    queue = [original.name]
    visited = set([original.name])

    # Map original node => new node
    # Get the name of the newly created item
    new_item_name = parent_doc.items[-1].name
    orig_to_new = {original.name: new_item_name}  # Use the actual name of the new item

    # BFS loop to traverse the entire hierarchy
    front = 0
    while front < len(queue):
        current_orig_id = queue[front]
        front = front + 1

        # The new parent's name for this original node
        current_new_id = orig_to_new.get(current_orig_id)

        # Fetch all children of the original node.
        try:
            # Use a direct SQL query to ensure we get all children
            # We only need to filter by parent_id, not parent (which is the VC document)
            children = frappe.db.sql(
                """
                SELECT name
                FROM `tabVelocetec Costing Detail`
                WHERE parent_id = %s
                AND parent = %s
            """,
                (current_orig_id, original.parent),
                as_dict=True,
            )

            # Add all children to the queue for processing
            if children:
                for child in children:
                    if child.name not in visited:
                        queue.append(child.name)
        except Exception as e:
            frappe.log_error(
                message=f"BFS Error: Failed to fetch children for node {current_orig_id}: {str(e)}",
                title="Tree Traversal Error",
            )
            children = []

        for child_info in children:
            # Skip if we've already processed this child
            if child_info.name in visited:
                continue

            # Mark as visited now to prevent duplicate processing
            visited.add(child_info.name)

            # Fetch the original child doc
            child_doc = frappe.get_doc("Velocetec Costing Detail", child_info.name)

            # Create a new child table entry as a dict
            new_child = {
                # Initialize basic fields to avoid None values
                "material_price": 0,
                "machining": 0,
                "design": 0,
                "finishing": 0,
                "inspection": 0,
                "other": 0,
                "mass_finishing": 0,
                "sub_con": 0,
                "fixings": 0,
                "tool_making": 0,
                "turning": 0,
                "edm": 0,
                "assembly": 0,
                "delivery_each": 0,
                "batch_cost": 0,
                "each_cost": 0,
                "sell_price_each": 0,
                "sell_cost_each": 0,
                "line_total": 0,
            }

            # Copy selected fields from the original child
            for df in child_doc.meta.fields:
                if df.fieldname in keep_fields:
                    new_child[df.fieldname] = child_doc.get(df.fieldname)

            # Copy additional fields or modify as needed
            new_child["description"] = child_doc.description
            new_child["dup_reference_part_number"] = child_doc.part_number
            new_child["dup_reference_part_number_name"] = child_doc.name
            new_child["part_number"] = "*copy*" + (child_doc.part_number or "")

            # Set parent_id to the new ID of the parent node
            new_child["parent_id"] = current_new_id

            new_child["is_dup_reference_created"] = (
                False  # Ensure this is set to False for proper VLC creation
            )

            # DO NOT copy the VLC reference - let the validate method create a new one
            # This prevents double VLC creation

            # Get the parent's part number for reference
            parent_doc_ref = frappe.get_doc("Velocetec Costing Detail", current_orig_id)
            pnum = parent_doc_ref.part_number or ""

            # Force child node status and set duplication flag
            new_child["is_child_part"] = 1
            new_child["skip_duplicate"] = 1

            # Set parent_part to the duplicated parent's part number
            new_child["parent_part"] = "*copy*" + pnum

            # Append the child to the parent document
            parent_doc.append("items", new_child)

            # Get the name of the newly created child item
            new_child_name = parent_doc.items[-1].name

            # Store the relationship between original and new item names
            orig_to_new[child_doc.name] = new_child_name

    # Save parent to persist the new child entry
    parent_doc.save(ignore_permissions=True)

    # Update parent references for all duplicated items
    update_parent_references(parent_doc)

    # Recalculate costs for all duplicated items
    recalculate_costs_for_duplicated_items(parent_doc)

    # Final verification of parent-child relationships
    verify_parent_child_relationships(parent_doc)

    # Force a recalculation of the grand total directly in the database
    # This avoids the need for a second save which can cause timestamp conflicts
    recalculate_grand_total(parent_doc)

    # Check for and fix any infinity values that might have been introduced
    fix_infinity_values(parent_doc)

    # Return the new root node's name.
    frappe.response["message"] = parent_doc.name


def update_parent_references(parent_doc):
    """
    Update parent references for all items in the document to ensure
    the hierarchical tree structure is maintained correctly.
    This function handles deep hierarchies (children of children).
    """
    try:
        # Create a mapping of part_number to item name for quick lookup
        part_number_map = {}
        duplicated_items = []

        # First pass: identify all duplicated items and build the part_number map
        for item in parent_doc.items:
            if item.part_number:
                part_number_map[item.part_number] = item.name

                # Identify duplicated items (those with part_number starting with "*copy*")
                if item.part_number.startswith("*copy*"):
                    duplicated_items.append(item)

        # Second pass: update parent_id for all duplicated items
        for item in duplicated_items:
            if item.is_child_part and item.parent_part:
                # If parent_part exists in our document, update parent_id
                if item.parent_part in part_number_map:
                    old_parent_id = item.parent_id
                    new_parent_id = part_number_map[item.parent_part]

                    # Only update if different
                    if old_parent_id != new_parent_id:
                        # Update directly in the database for immediate effect
                        frappe.db.set_value(
                            "Velocetec Costing Detail",
                            item.name,
                            "parent_id",
                            new_parent_id,
                        )

                        # Also update the in-memory object
                        item.parent_id = new_parent_id

        # Commit changes to ensure they're saved
        frappe.db.commit()

        # Reload the document to ensure all changes are reflected
        parent_doc.reload()
    except Exception as e:
        frappe.log_error(
            message=f"Error updating parent references for document {parent_doc.name}:\n{str(e)}",
            title="Tree Duplication Error",
        )


def recalculate_costs_for_duplicated_items(parent_doc):
    """
    Recalculate costs for all duplicated items in the document.
    Also updates VLC part numbers to match the duplicated items.
    Ensures all numeric values are properly handled to prevent infinity issues.
    """
    try:
        # Find all duplicated items (those with part_number starting with "*copy*")
        for item in parent_doc.items:
            if item.part_number and item.part_number.startswith("*copy*"):
                # If this item has a reference to the original item
                if item.dup_reference_part_number_name:
                    try:
                        # Get the original item's costs
                        original = frappe.get_doc(
                            "Velocetec Costing Detail",
                            item.dup_reference_part_number_name,
                        )

                        # Directly copy numeric values without any string concatenation or manipulation
                        numeric_fields = [
                            "batch_cost",
                            "each_cost",
                            "sell_price_each",
                            "sell_cost_each",
                            "material_price",
                            "machining",
                            "inspection",
                            "design",
                            "mass_finishing",
                            "finishing",
                            "edm",
                            "other",
                            "sub_con",
                            "assembly",
                            "tool_making",
                            "turning",
                            "fixings",
                        ]

                        # Don't reset all fields to 0 first, as this causes the duplicated node to lose values
                        # Instead, we'll handle each field individually

                        # Copy the values from the original item to the duplicated item
                        for field in numeric_fields:
                            try:
                                # Get the original value
                                orig_value = original.get(field)

                                # Only proceed if the original value exists
                                if orig_value is not None:
                                    try:
                                        # Convert to float regardless of type
                                        safe_value = float(orig_value)

                                        # Cap extremely large values to prevent infinity issues
                                        if safe_value > 1e10:  # Cap at 10 billion
                                            safe_value = 0
                                            frappe.log_error(
                                                f"Value too large for {field} in {item.name}: {orig_value}",
                                                "Value Capping",
                                            )

                                        # Set the value directly as a float
                                        frappe.db.set_value(
                                            "Velocetec Costing Detail",
                                            item.name,
                                            field,
                                            safe_value,
                                        )
                                    except (ValueError, TypeError):
                                        # If conversion fails, try to use the original value directly
                                        try:
                                            frappe.db.set_value(
                                                "Velocetec Costing Detail",
                                                item.name,
                                                field,
                                                orig_value,
                                            )
                                        except Exception:
                                            # If that fails too, set to 0
                                            frappe.db.set_value(
                                                "Velocetec Costing Detail",
                                                item.name,
                                                field,
                                                0,
                                            )
                            except Exception as e:
                                frappe.log_error(
                                    f"Error setting {field} for {item.name}: {str(e)}",
                                    "Field Setting Error",
                                )
                                # Continue with other fields if one fails
                                pass

                        # Calculate line_total safely using the duplicated item's values
                        try:
                            # Get the duplicated item's current values
                            duplicated_item = frappe.get_doc(
                                "Velocetec Costing Detail", item.name
                            )

                            # Use the duplicated item's sell_price_each, not the original's
                            sell_price = float(duplicated_item.sell_price_each or 0)
                            quantity = float(duplicated_item.quantity or 1)
                            line_total = sell_price * quantity

                            # Cap extremely large values
                            if line_total > 1e10:  # Cap at 10 billion
                                # If the line_total is too large, try using the original's values
                                sell_price = float(original.sell_price_each or 0)
                                line_total = sell_price * quantity

                                # Check again
                                if line_total > 1e10:
                                    line_total = 0

                            frappe.db.set_value(
                                "Velocetec Costing Detail",
                                item.name,
                                "line_total",
                                line_total,
                            )
                        except Exception as e:
                            frappe.log_error(
                                f"Error calculating line_total for {item.name}: {str(e)}",
                                "Line Total Error",
                            )
                            # Try to use the original's line_total
                            try:
                                orig_line_total = float(original.line_total or 0)
                                if (
                                    orig_line_total <= 1e10
                                ):  # Check if it's a reasonable value
                                    frappe.db.set_value(
                                        "Velocetec Costing Detail",
                                        item.name,
                                        "line_total",
                                        orig_line_total,
                                    )
                                else:
                                    frappe.db.set_value(
                                        "Velocetec Costing Detail",
                                        item.name,
                                        "line_total",
                                        0,
                                    )
                            except Exception:
                                # Set a safe default
                                frappe.db.set_value(
                                    "Velocetec Costing Detail",
                                    item.name,
                                    "line_total",
                                    0,
                                )

                        # If the original has a VLC, check if a VLC already exists for this item
                        if original.velocetec_line_costing:
                            # Check if a VLC already exists for this item
                            existing_vlc = frappe.db.get_value(
                                "Velocetec Line Costing",
                                {"velocetec_costing_detail": item.name},
                                "name",
                            )

                            if existing_vlc:
                                # A VLC already exists, just mark as created
                                frappe.db.set_value(
                                    "Velocetec Costing Detail",
                                    item.name,
                                    "is_dup_reference_created",
                                    1,
                                )

                                # Update the VLC part number to match the duplicated item
                                vlc_doc = frappe.get_doc(
                                    "Velocetec Line Costing", existing_vlc
                                )
                                if vlc_doc.part_number != item.part_number:
                                    vlc_doc.part_number = item.part_number
                                    vlc_doc.save()

                                # Update the VLC reference in the item
                                frappe.db.set_value(
                                    "Velocetec Costing Detail",
                                    item.name,
                                    "velocetec_line_costing",
                                    json.dumps(
                                        vlc_doc.as_dict(no_nulls=True), default=str
                                    ),
                                )
                            else:
                                # No VLC exists yet, let the validate method create it
                                # DO NOT create a new VLC here to prevent duplication
                                pass
                    except Exception as e:
                        frappe.log_error(
                            f"Error processing duplicated item {item.name}: {str(e)}",
                            "Item Processing Error",
                        )
                        # Continue with other items if one fails
                        pass

        # Commit the changes
        frappe.db.commit()
    except Exception as e:
        frappe.log_error(
            message=f"Error recalculating costs for duplicated items in {parent_doc.name}:\n{str(e)}",
            title="Cost Recalculation Error",
        )


def update_vlc_part_number(item):
    """
    Update the part number in the VLC to match the duplicated item's part number.
    This ensures the VLC part number is correctly updated after duplication.
    """
    try:
        if not item.velocetec_line_costing:
            return

        # Parse the VLC data
        vlc_data = None
        try:
            vlc_data = json.loads(item.velocetec_line_costing)
        except Exception:
            return

        if not vlc_data or not vlc_data.get("name"):
            return

        # Update the part number in the VLC document
        vlc_name = vlc_data.get("name")
        if frappe.db.exists("Velocetec Line Costing", vlc_name):
            # Update the part number directly in the database
            frappe.db.set_value(
                "Velocetec Line Costing", vlc_name, "part_number", item.part_number
            )

            # Get the updated VLC document
            updated_vlc = frappe.get_doc("Velocetec Line Costing", vlc_name)

            # Update the VLC reference in the item
            frappe.db.set_value(
                "Velocetec Costing Detail",
                item.name,
                "velocetec_line_costing",
                json.dumps(updated_vlc.as_dict(no_nulls=True), default=str),
            )
    except Exception as e:
        frappe.log_error(
            message=f"Error updating VLC part number for {item.name}: {str(e)}",
            title="VLC Update Error",
        )


def recalculate_grand_total(parent_doc):
    """
    Recalculate the grand total for the document based on the line totals.
    This ensures the grand total is correct after duplication.
    Updates only the database, not the document object, to avoid timestamp conflicts.
    Handles numeric values safely to prevent infinity issues.
    """
    try:
        # Get all items directly from the database to ensure we have the latest data
        items = frappe.get_all(
            "Velocetec Costing Detail",
            filters={"parent": parent_doc.name},
            fields=["name", "is_child_part", "line_total"],
        )

        # Calculate the sum of all parent item line totals
        line_total_sum = 0
        for item in items:
            if not item.get(
                "is_child_part"
            ):  # Only include parent items to avoid double-counting
                try:
                    # Get the line total and ensure it's a valid float
                    line_total = item.get("line_total")
                    if line_total is not None:
                        try:
                            # Convert to float and check for extremely large values
                            safe_value = float(line_total)
                            if safe_value > 1e10:  # Cap at 10 billion
                                safe_value = 0
                                frappe.log_error(
                                    f"Line total too large for item {item.get('name')}: {line_total}",
                                    "Value Capping",
                                )
                            line_total_sum += safe_value
                        except (ValueError, TypeError):
                            # If conversion fails, don't add anything
                            pass
                except Exception as e:
                    frappe.log_error(
                        f"Error processing line total for item {item.get('name')}: {str(e)}",
                        "Line Total Processing Error",
                    )

        # Cap the grand total to prevent extremely large values
        if line_total_sum > 1e10:  # Cap at 10 billion
            line_total_sum = 0
            frappe.log_error(
                f"Grand total too large for document {parent_doc.name}: {line_total_sum}",
                "Grand Total Capping",
            )

        # Update directly in the database without touching the document object
        frappe.db.set_value(
            "Velocetec Costing", parent_doc.name, "grand_total", line_total_sum
        )

        # Commit the changes
        frappe.db.commit()
    except Exception as e:
        frappe.log_error(
            message=f"Error recalculating grand total for {parent_doc.name}: {str(e)}",
            title="Grand Total Calculation Error",
        )


def fix_infinity_values(parent_doc):
    """
    Check for and fix any infinity or extremely large values in the document.
    This is a safety measure to prevent issues with numeric values.
    """
    try:
        # Get all items directly from the database
        items = frappe.get_all(
            "Velocetec Costing Detail",
            filters={"parent": parent_doc.name},
            fields=["name", "batch_cost", "each_cost", "sell_price_each", "line_total"],
        )

        # Check each item for extremely large values
        for item in items:
            needs_reset = False

            # Check numeric fields for extremely large values
            for field in ["batch_cost", "each_cost", "sell_price_each", "line_total"]:
                try:
                    value = item.get(field)
                    if value is not None:
                        # Try to convert to float
                        try:
                            float_value = float(value)
                            # Check if it's extremely large
                            if (
                                float_value > 1e10 or float_value < -1e10
                            ):  # Cap at ±10 billion
                                needs_reset = True
                                break
                        except (ValueError, TypeError):
                            # If it can't be converted to float, it's probably a string representation of a large number
                            if isinstance(value, str) and len(value) > 10:
                                needs_reset = True
                                break
                except Exception:
                    # If there's any error processing the value, assume it needs reset
                    needs_reset = True
                    break

            # If any field has an extremely large value, only reset that specific field
            if needs_reset:
                # Get the original item if this is a duplicated item
                original_item = None
                try:
                    # Check if this is a duplicated item
                    item_doc = frappe.get_doc(
                        "Velocetec Costing Detail", item.get("name")
                    )
                    if item_doc.dup_reference_part_number_name:
                        # Get the original item
                        original_item = frappe.get_doc(
                            "Velocetec Costing Detail",
                            item_doc.dup_reference_part_number_name,
                        )
                except Exception:
                    pass

                numeric_fields = [
                    "batch_cost",
                    "each_cost",
                    "sell_price_each",
                    "sell_cost_each",
                    "material_price",
                    "machining",
                    "inspection",
                    "design",
                    "mass_finishing",
                    "finishing",
                    "edm",
                    "other",
                    "sub_con",
                    "assembly",
                    "tool_making",
                    "turning",
                    "fixings",
                    "line_total",
                ]

                for field in numeric_fields:
                    try:
                        value = item.get(field)
                        is_large_value = False

                        # Check if the value is extremely large
                        try:
                            if value is not None:
                                float_value = float(value)
                                if float_value > 1e10 or float_value < -1e10:
                                    is_large_value = True
                        except (ValueError, TypeError):
                            if isinstance(value, str) and len(value) > 10:
                                is_large_value = True

                        # Only reset fields with extremely large values
                        if is_large_value:
                            # If we have the original item, copy its value
                            if original_item and hasattr(original_item, field):
                                orig_value = getattr(original_item, field)
                                try:
                                    # Convert to float and ensure it's not too large
                                    safe_value = float(orig_value or 0)
                                    if safe_value > 1e10 or safe_value < -1e10:
                                        safe_value = 0

                                    # Set the value from the original item
                                    frappe.db.set_value(
                                        "Velocetec Costing Detail",
                                        item.get("name"),
                                        field,
                                        safe_value,
                                    )
                                except (ValueError, TypeError):
                                    # If conversion fails, set to 0
                                    frappe.db.set_value(
                                        "Velocetec Costing Detail",
                                        item.get("name"),
                                        field,
                                        0,
                                    )
                            else:
                                # If no original item, just reset to 0
                                frappe.db.set_value(
                                    "Velocetec Costing Detail",
                                    item.get("name"),
                                    field,
                                    0,
                                )
                    except Exception:
                        # If there's any error, just continue to the next field
                        pass

                frappe.log_error(
                    f"Fixed extremely large values for item {item.get('name')}",
                    "Value Fix",
                )

        # Check if the grand total is extremely large
        grand_total = frappe.db.get_value(
            "Velocetec Costing", parent_doc.name, "grand_total"
        )
        try:
            if grand_total is not None:
                float_grand_total = float(grand_total)
                if (
                    float_grand_total > 1e10 or float_grand_total < -1e10
                ):  # Cap at ±10 billion
                    # Reset the grand total
                    frappe.db.set_value(
                        "Velocetec Costing", parent_doc.name, "grand_total", 0
                    )
                    frappe.log_error(
                        f"Reset grand total for document {parent_doc.name} due to extremely large value: {grand_total}",
                        "Grand Total Reset",
                    )
        except (ValueError, TypeError):
            # If it can't be converted to float, reset it
            frappe.db.set_value("Velocetec Costing", parent_doc.name, "grand_total", 0)
            frappe.log_error(
                f"Reset grand total for document {parent_doc.name} due to invalid value: {grand_total}",
                "Grand Total Reset",
            )

        # Commit the changes
        frappe.db.commit()
    except Exception as e:
        frappe.log_error(
            message=f"Error fixing infinity values for {parent_doc.name}: {str(e)}",
            title="Infinity Fix Error",
        )


def verify_parent_child_relationships(parent_doc):
    """
    Verify that all parent-child relationships are correctly set.
    This is a final check to ensure the hierarchical structure is maintained.
    """
    try:
        # Get all duplicated items and original items
        duplicated_items = []
        original_items = []
        part_number_map = {}
        dup_ref_map = {}  # Map from original name to duplicated item

        # Build maps of part numbers to items
        for item in parent_doc.items:
            if item.part_number:
                part_number_map[item.part_number] = item.name

                if item.part_number.startswith("*copy*"):
                    duplicated_items.append(item)
                    if item.dup_reference_part_number_name:
                        dup_ref_map[item.dup_reference_part_number_name] = item
                else:
                    original_items.append(item)

        # Check each duplicated item
        for item in duplicated_items:
            if item.is_child_part and item.parent_part:
                # The parent part should exist in our document
                if item.parent_part in part_number_map:
                    expected_parent_id = part_number_map[item.parent_part]

                    # If parent_id doesn't match expected_parent_id, fix it
                    if item.parent_id != expected_parent_id:
                        # Update directly in the database
                        frappe.db.set_value(
                            "Velocetec Costing Detail",
                            item.name,
                            "parent_id",
                            expected_parent_id,
                        )
                else:
                    # Parent part not found - this is a potential issue but not critical
                    pass

        # Check for missing duplicated nodes
        for orig_item in original_items:
            # If this is a child node, it should have a corresponding duplicated node
            if orig_item.is_child_part:
                # Check if this node was duplicated
                if orig_item.name not in dup_ref_map:
                    # This node is missing from the duplicated structure
                    # We'll handle this in a future update if needed
                    pass

        # Commit changes
        frappe.db.commit()
    except Exception as e:
        frappe.log_error(
            message=f"Error verifying parent-child relationships: {str(e)}",
            title="Verification Error",
        )


@frappe.whitelist()
def get_parent_id(parent_part_number, parent):
    """Fetch child parts for a given parent part number."""

    name = frappe.db.get_value(
        "Velocetec Costing Detail",
        {"parent": parent, "part_number": parent_part_number},
        "name",
    )

    frappe.response["message"] = name


@frappe.whitelist()
def get_tree_structure_preview(part_number, current_vc_name):
    """
    Get a preview of the tree structure that will be rebuilt for the given part.
    Returns the complete hierarchy with part details but no cost information.
    """
    try:
        # Find the latest VC record for this part number (excluding current document)
        latest_vc = frappe.db.sql(
            """
            SELECT DISTINCT vc.name, vc.creation
            FROM `tabVelocetec Costing` vc
            INNER JOIN `tabVelocetec Costing Detail` vcd ON vc.name = vcd.parent
            WHERE vcd.part_number = %(part_number)s
            AND vc.name != %(current_vc_name)s
            ORDER BY vc.creation DESC
            LIMIT 1
        """,
            {"part_number": part_number, "current_vc_name": current_vc_name},
            as_dict=True,
        )

        if not latest_vc:
            return {
                "success": False,
                "message": f"No existing VC records found for part {part_number}",
            }

        latest_vc_name = latest_vc[0].name

        # Get all parts from the latest VC
        source_parts = frappe.db.sql(
            """
            SELECT name, part_number, description, quantity, is_child_part,
                   parent_part, parent_id, delivery_date, notes
            FROM `tabVelocetec Costing Detail`
            WHERE parent = %(latest_vc_name)s
            ORDER BY idx
        """,
            {"latest_vc_name": latest_vc_name},
            as_dict=True,
        )

        if not source_parts:
            return {
                "success": False,
                "message": f"No parts found in latest VC record {latest_vc_name}",
            }

        # Find the entered part
        entered_part = None
        for part in source_parts:
            if part.part_number == part_number:
                entered_part = part
                break

        if not entered_part:
            return {
                "success": False,
                "message": f"Part {part_number} not found in latest VC record",
            }

        # Use the entered part as the root (no need to find parent)
        # This will show only the entered part and its descendants
        root_part = entered_part

        # Clean the entered part's parent relationships since it's now the root
        # Create a copy to avoid modifying the original data
        import copy

        root_part_cleaned = copy.deepcopy(root_part)
        root_part_cleaned.is_child_part = False
        root_part_cleaned.parent_part = None
        root_part_cleaned.parent_id = None

        # Build the hierarchy starting from the cleaned entered part (downward only)
        parts_to_rebuild = []
        _build_part_hierarchy(source_parts, root_part_cleaned, parts_to_rebuild)

        # Generate tree structure for visualization
        tree_structure = _generate_tree_structure(
            parts_to_rebuild, root_part_cleaned, part_number
        )

        return {
            "success": True,
            "tree_structure": tree_structure,
            "root_part": root_part.part_number,
            "entered_part": part_number,
            "total_parts": len(parts_to_rebuild),
            "source_vc": latest_vc_name,
            "message": f"Found tree structure with {len(parts_to_rebuild)} parts starting from {part_number} and its descendants",
        }

    except Exception as e:
        frappe.log_error(
            f"Error getting tree structure preview: {str(e)}", "Tree Preview Error"
        )
        return {
            "success": False,
            "message": f"Error getting tree preview: {str(e)}",
        }


@frappe.whitelist()
def enqueue_selective_tree_rebuild(source_part_number, current_vc_name, selected_parts):
    """
    Enqueue selective tree rebuild for specific parts from a tree structure.
    This works like automatic tree rebuild but only processes selected parts.
    """
    import json

    try:
        selected_parts_list = (
            json.loads(selected_parts)
            if isinstance(selected_parts, str)
            else selected_parts
        )
    except (json.JSONDecodeError, TypeError):
        return {"success": False, "message": "Invalid selected parts data format"}

    if not selected_parts_list:
        return {"success": False, "message": "No parts selected for rebuild"}

    job_name = f"selective_rebuild_{current_vc_name}_{source_part_number}"

    # Check if job is already running
    if frappe.get_all(
        "RQ Job",
        filters={"job_name": job_name, "status": ["in", ["queued", "started"]]},
    ):
        return {
            "success": False,
            "message": "A selective tree rebuild is already running for this part. Please wait for it to complete.",
        }

    # Enqueue the background job
    frappe.enqueue(
        method="velocetec.api.velocetec_costing.selective_tree_rebuild",
        queue="default",
        timeout=600,  # 10-minute timeout for complex trees
        is_async=True,
        job_name=job_name,
        source_part_number=source_part_number,
        current_vc_name=current_vc_name,
        selected_parts=selected_parts_list,
    )

    return {
        "success": True,
        "message": f"Selective tree rebuild started for {len(selected_parts_list)} selected parts. This will include VLC data if available.",
        "job_name": job_name,
    }


@frappe.whitelist()
def enqueue_load_parts_from_latest_vc(part_number, current_vc_name):
    """
    Enqueue Background Job 1: Load parts and sub-parts (including all children) into VC (Velocetec Costing)
    Use the latest record from VC based on date and build the complete hierarchical structure
    """
    job_name = f"load_parts_vc_{current_vc_name}_{part_number}"

    # Check if job is already running
    if frappe.get_all(
        "RQ Job",
        filters={"job_name": job_name, "status": ["in", ["queued", "started"]]},
    ):
        return {
            "success": False,
            "message": "A similar job is already running. Please wait for it to complete.",
        }

    # Enqueue the background job
    frappe.enqueue(
        method="velocetec.api.velocetec_costing.load_parts_from_latest_vc",
        queue="default",
        timeout=300,
        is_async=True,
        job_name=job_name,
        part_number=part_number,
        current_vc_name=current_vc_name,
    )

    return {
        "success": True,
        "message": f"Background job started to load parts from latest VC. Job ID: {job_name}",
        "job_name": job_name,
    }


def load_parts_from_latest_vc(part_number, current_vc_name):
    """
    Background Job 1: Load parts and sub-parts (including all children) into VC (Velocetec Costing)
    Use the latest record from VC based on date and build the complete hierarchical structure
    """
    try:
        # Find the latest VC record for this part number (excluding current document)
        latest_vc = frappe.db.sql(
            """
            SELECT DISTINCT vc.name, vc.creation
            FROM `tabVelocetec Costing` vc
            INNER JOIN `tabVelocetec Costing Detail` vcd ON vc.name = vcd.parent
            WHERE vcd.part_number = %(part_number)s
            AND vc.name != %(current_vc_name)s
            ORDER BY vc.creation DESC
            LIMIT 1
        """,
            {"part_number": part_number, "current_vc_name": current_vc_name},
            as_dict=True,
        )

        if not latest_vc:
            return {
                "success": False,
                "message": f"No existing VC records found for part {part_number}",
            }

        latest_vc_name = latest_vc[0].name

        # Get the current VC document
        current_vc_doc = frappe.get_doc("Velocetec Costing", current_vc_name)

        # Get all parts from the latest VC that match the part number or are children of it
        source_parts = frappe.db.sql(
            """
            SELECT *
            FROM `tabVelocetec Costing Detail`
            WHERE parent = %(latest_vc_name)s
            ORDER BY idx
        """,
            {"latest_vc_name": latest_vc_name},
            as_dict=True,
        )

        if not source_parts:
            return {
                "success": False,
                "message": f"No parts found in latest VC record {latest_vc_name}",
            }

        # Find the root part and build hierarchy
        root_part = None
        for part in source_parts:
            if part.part_number == part_number and not part.is_child_part:
                root_part = part
                break

        if not root_part:
            return {
                "success": False,
                "message": f"Root part {part_number} not found in latest VC record",
            }

        # Build the complete hierarchy starting from root part
        parts_to_load = []
        _build_part_hierarchy(source_parts, root_part, parts_to_load)

        # Load parts into current VC
        for part_data in parts_to_load:
            # Create new part in current VC
            new_part = {
                "part_number": part_data.part_number,
                "description": part_data.description,
                "quantity": part_data.quantity,
                "is_child_part": part_data.is_child_part,
                "parent_part": part_data.parent_part,
                "delivery_date": part_data.delivery_date,
                "mark_up_item": part_data.mark_up_item,
                "delivery_type": part_data.delivery_type,
                "delivery_cost": part_data.delivery_cost,
                "delivery_total": part_data.delivery_total,
                "notes": part_data.notes,
            }

            current_vc_doc.append("items", new_part)

        # Save the document
        current_vc_doc.save(ignore_permissions=True)

        # Notify the user via realtime
        frappe.publish_realtime(
            event="part_loading_complete",
            message={
                "success": True,
                "message": f"Successfully loaded {len(parts_to_load)} parts from latest VC record",
                "loaded_parts": len(parts_to_load),
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {
            "success": True,
            "message": f"Successfully loaded {len(parts_to_load)} parts from latest VC record",
            "loaded_parts": len(parts_to_load),
        }

    except Exception as e:
        frappe.log_error(
            f"Error loading parts from latest VC: {str(e)}", "Load Parts from VC Error"
        )

        # Notify the user of the error via realtime
        frappe.publish_realtime(
            event="part_loading_error",
            message={
                "success": False,
                "message": f"Error loading parts: {str(e)}",
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {"success": False, "message": f"Error loading parts: {str(e)}"}


@frappe.whitelist()
def enqueue_create_parts_from_latest_vlc(part_number, current_vc_name):
    """
    Enqueue Background Job 2: Create parts and sub-parts (including all children) into VLC (Velocetec Labor Costing)
    Use the latest record from VLC and build the same hierarchical structure
    """
    job_name = f"create_parts_vlc_{current_vc_name}_{part_number}"

    # Check if job is already running
    if frappe.get_all(
        "RQ Job",
        filters={"job_name": job_name, "status": ["in", ["queued", "started"]]},
    ):
        return {
            "success": False,
            "message": "A similar job is already running. Please wait for it to complete.",
        }

    # Enqueue the background job
    frappe.enqueue(
        method="velocetec.api.velocetec_costing.create_parts_from_latest_vlc",
        queue="default",
        timeout=300,
        is_async=True,
        job_name=job_name,
        part_number=part_number,
        current_vc_name=current_vc_name,
    )

    return {
        "success": True,
        "message": f"Background job started to create parts from latest VLC. Job ID: {job_name}",
        "job_name": job_name,
    }


def create_parts_from_latest_vlc(part_number, current_vc_name):
    """
    Background Job 2: Create parts and sub-parts (including all children) into VLC (Velocetec Labor Costing)
    Use the latest record from VLC and build the same hierarchical structure
    """
    try:
        # Find the latest VLC record for this part number
        latest_vlc = frappe.db.sql(
            """
            SELECT vlc.name, vlc.creation, vlc.velocetec_costing, vlc.velocetec_costing_detail
            FROM `tabVelocetec Line Costing` vlc
            WHERE vlc.part_number = %(part_number)s
            ORDER BY vlc.creation DESC
            LIMIT 1
        """,
            {"part_number": part_number},
            as_dict=True,
        )

        if not latest_vlc:
            return {
                "success": False,
                "message": f"No existing VLC records found for part {part_number}",
            }

        latest_vlc_name = latest_vlc[0].name
        latest_vlc_vc = latest_vlc[0].velocetec_costing

        # Get the current VC document
        current_vc_doc = frappe.get_doc("Velocetec Costing", current_vc_name)

        # Get the source VC structure to understand the hierarchy
        source_parts = frappe.db.sql(
            """
            SELECT *
            FROM `tabVelocetec Costing Detail`
            WHERE parent = %(latest_vlc_vc)s
            ORDER BY idx
        """,
            {"latest_vlc_vc": latest_vlc_vc},
            as_dict=True,
        )

        if not source_parts:
            return {
                "success": False,
                "message": f"No parts found in source VC record {latest_vlc_vc}",
            }

        # Find the root part and build hierarchy
        root_part = None
        for part in source_parts:
            if part.part_number == part_number and not part.is_child_part:
                root_part = part
                break

        if not root_part:
            return {
                "success": False,
                "message": f"Root part {part_number} not found in source VC record",
            }

        # Build the complete hierarchy
        parts_to_create = []
        _build_part_hierarchy(source_parts, root_part, parts_to_create)

        # Create parts in current VC and their corresponding VLCs
        created_parts = []
        part_name_mapping = (
            {}
        )  # Map old part names to new part names for parent references

        for part_data in parts_to_create:
            # Create new part in current VC
            new_part = {
                "part_number": part_data.part_number,
                "description": part_data.description,
                "quantity": part_data.quantity,
                "is_child_part": part_data.is_child_part,
                "parent_part": part_data.parent_part,
                "delivery_date": part_data.delivery_date,
                "mark_up_item": part_data.mark_up_item,
                "delivery_type": part_data.delivery_type,
                "delivery_cost": part_data.delivery_cost,
                "delivery_total": part_data.delivery_total,
                "notes": part_data.notes,
            }

            current_vc_doc.append("items", new_part)

        # Save the VC document first to get the new part names
        current_vc_doc.save(ignore_permissions=True)

        # Now create VLCs for each part
        for i, part_data in enumerate(parts_to_create):
            new_part_name = current_vc_doc.items[-(len(parts_to_create) - i)].name
            part_name_mapping[part_data.name] = new_part_name

            # Find corresponding VLC from source
            source_vlc = frappe.db.sql(
                """
                SELECT vlc.*
                FROM `tabVelocetec Line Costing` vlc
                WHERE vlc.velocetec_costing = %(source_vc)s
                AND vlc.velocetec_costing_detail = %(source_part_name)s
                ORDER BY vlc.creation DESC
                LIMIT 1
            """,
                {"source_vc": latest_vlc_vc, "source_part_name": part_data.name},
                as_dict=True,
            )

            if source_vlc:
                # Create new VLC based on source VLC
                source_vlc_doc = frappe.get_doc(
                    "Velocetec Line Costing", source_vlc[0].name
                )
                _create_vlc_from_source(
                    source_vlc_doc,
                    current_vc_name,
                    new_part_name,
                    part_data.part_number,
                )
                created_parts.append(part_data.part_number)

        # Notify the user via realtime
        frappe.publish_realtime(
            event="part_creation_complete",
            message={
                "success": True,
                "message": f"Successfully created {len(created_parts)} parts with VLCs from latest VLC record",
                "created_parts": len(created_parts),
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {
            "success": True,
            "message": f"Successfully created {len(created_parts)} parts with VLCs from latest VLC record",
            "created_parts": len(created_parts),
        }

    except Exception as e:
        frappe.log_error(
            f"Error creating parts from latest VLC: {str(e)}",
            "Create Parts from VLC Error",
        )

        # Notify the user of the error via realtime
        frappe.publish_realtime(
            event="part_creation_error",
            message={
                "success": False,
                "message": f"Error creating parts: {str(e)}",
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {"success": False, "message": f"Error creating parts: {str(e)}"}


def selective_tree_rebuild(source_part_number, current_vc_name, selected_parts):
    """
    Rebuild tree structure for selected parts only from latest VC record containing the part.
    This works like automatic_tree_rebuild but only processes selected parts.
    """
    try:
        # Find the latest VC record for this part number (excluding current document)
        latest_vc = frappe.db.sql(
            """
            SELECT DISTINCT vc.name, vc.creation
            FROM `tabVelocetec Costing` vc
            INNER JOIN `tabVelocetec Costing Detail` vcd ON vc.name = vcd.parent
            WHERE vcd.part_number = %(part_number)s
            AND vc.name != %(current_vc_name)s
            ORDER BY vc.creation DESC
            LIMIT 1
        """,
            {"part_number": source_part_number, "current_vc_name": current_vc_name},
            as_dict=True,
        )

        if not latest_vc:
            frappe.publish_realtime(
                event="tree_rebuild_error",
                message={
                    "success": False,
                    "message": f"No existing VC records found for part {source_part_number}",
                    "vc_name": current_vc_name,
                },
                user=frappe.session.user,
            )
            return {
                "success": False,
                "message": f"No existing VC records found for part {source_part_number}",
            }

        latest_vc_name = latest_vc[0].name

        # Get the current VC document
        current_vc_doc = frappe.get_doc("Velocetec Costing", current_vc_name)

        # Get all parts from the latest VC
        source_parts = frappe.db.sql(
            """
            SELECT *
            FROM `tabVelocetec Costing Detail`
            WHERE parent = %(latest_vc_name)s
            ORDER BY idx
        """,
            {"latest_vc_name": latest_vc_name},
            as_dict=True,
        )

        if not source_parts:
            frappe.publish_realtime(
                event="tree_rebuild_error",
                message={
                    "success": False,
                    "message": f"No parts found in latest VC record {latest_vc_name}",
                    "vc_name": current_vc_name,
                },
                user=frappe.session.user,
            )
            return {
                "success": False,
                "message": f"No parts found in latest VC record {latest_vc_name}",
            }

        # Filter source parts to only include selected parts
        parts_to_rebuild = []
        for part in source_parts:
            if part.part_number in selected_parts:
                parts_to_rebuild.append(part)

        if not parts_to_rebuild:
            frappe.publish_realtime(
                event="tree_rebuild_error",
                message={
                    "success": False,
                    "message": f"None of the selected parts found in latest VC record {latest_vc_name}",
                    "vc_name": current_vc_name,
                },
                user=frappe.session.user,
            )
            return {
                "success": False,
                "message": f"None of the selected parts found in latest VC record {latest_vc_name}",
            }

        # Clean the entered part's parent relationships since it's now the root
        # Find and clean the entered part in the parts_to_rebuild list
        import copy

        for i, part in enumerate(parts_to_rebuild):
            if part.part_number == source_part_number:
                # Create a cleaned copy of the entered part
                cleaned_part = copy.deepcopy(part)
                cleaned_part.is_child_part = False
                cleaned_part.parent_part = None
                cleaned_part.parent_id = None
                parts_to_rebuild[i] = cleaned_part
                break

        # Create parts in current VC
        created_parts = []
        part_name_mapping = {}  # Map old part names to new part names

        for part_data in parts_to_rebuild:
            # Create new part in current VC
            new_part = {
                "part_number": part_data.part_number,
                "description": part_data.description,
                "quantity": part_data.quantity,
                "is_child_part": part_data.is_child_part,
                "parent_part": part_data.parent_part,
                "delivery_date": part_data.delivery_date,
                "mark_up_item": part_data.mark_up_item,
                "delivery_type": part_data.delivery_type,
                "delivery_cost": part_data.delivery_cost,
                "delivery_total": part_data.delivery_total,
                "notes": part_data.notes,
            }

            current_vc_doc.append("items", new_part)

        # Save the VC document first to get the new part names
        current_vc_doc.save(ignore_permissions=True)

        # Create mapping of old to new part names
        for i, part_data in enumerate(parts_to_rebuild):
            new_part_name = current_vc_doc.items[-(len(parts_to_rebuild) - i)].name
            part_name_mapping[part_data.name] = new_part_name

        # Now create VLCs for each part that had VLC data
        vlc_created_count = 0
        for part_data in parts_to_rebuild:
            new_part_name = part_name_mapping[part_data.name]

            # Check if original part had VLC data
            source_vlc = frappe.db.sql(
                """
                SELECT vlc.*
                FROM `tabVelocetec Line Costing` vlc
                WHERE vlc.velocetec_costing = %(source_vc)s
                AND vlc.velocetec_costing_detail = %(source_part_name)s
                ORDER BY vlc.creation DESC
                LIMIT 1
            """,
                {"source_vc": latest_vc_name, "source_part_name": part_data.name},
                as_dict=True,
            )

            if source_vlc:
                # Create new VLC based on source VLC
                source_vlc_doc = frappe.get_doc(
                    "Velocetec Line Costing", source_vlc[0].name
                )
                new_vlc = _create_vlc_from_source(
                    source_vlc_doc,
                    current_vc_name,
                    new_part_name,
                    part_data.part_number,
                )
                if new_vlc:
                    vlc_created_count += 1

            created_parts.append(part_data.part_number)

        # Notify the user via realtime
        frappe.publish_realtime(
            event="tree_rebuild_complete",
            message={
                "success": True,
                "message": f"Successfully rebuilt {len(created_parts)} selected parts: {vlc_created_count} VLCs created from latest VC record",
                "created_parts": len(created_parts),
                "vlc_created": vlc_created_count,
                "source_vc": latest_vc_name,
                "selected_parts": len(selected_parts),
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {
            "success": True,
            "message": f"Successfully rebuilt {len(created_parts)} selected parts: {vlc_created_count} VLCs created",
            "created_parts": len(created_parts),
            "vlc_created": vlc_created_count,
            "selected_parts": len(selected_parts),
        }

    except Exception as e:
        frappe.log_error(
            f"Error in selective tree rebuild: {str(e)}", "Selective Tree Rebuild Error"
        )

        # Notify the user of the error via realtime
        frappe.publish_realtime(
            event="tree_rebuild_error",
            message={
                "success": False,
                "message": f"Error rebuilding selected parts: {str(e)}",
                "vc_name": current_vc_name,
            },
            user=frappe.session.user,
        )

        return {
            "success": False,
            "message": f"Error rebuilding selected parts: {str(e)}",
        }


def _generate_tree_structure(parts_list, root_part, entered_part_number):
    """
    Generate a hierarchical tree structure for visualization.
    Returns a nested structure showing the complete hierarchy.
    """

    def build_tree_node(part, level=0):
        """Build a tree node with part information"""
        is_entered_part = part.part_number == entered_part_number

        node = {
            "part_number": part.part_number,
            "description": part.description or "",
            "quantity": part.quantity or 1,
            "is_root": is_entered_part,  # The entered part is now the root
            "is_entered_part": is_entered_part,
            "level": level,
            "children": [],
            "delivery_date": part.delivery_date or "",
            "notes": part.notes or "",
        }

        # Find direct children of this part
        children = []
        for child_part in parts_list:
            if child_part.is_child_part and (
                (child_part.parent_id and child_part.parent_id == part.name)
                or (
                    not child_part.parent_id
                    and child_part.parent_part == part.part_number
                )
            ):
                children.append(child_part)

        # Recursively build children nodes
        for child in children:
            child_node = build_tree_node(child, level + 1)
            node["children"].append(child_node)

        return node

    # Build the complete tree starting from root
    tree = build_tree_node(root_part, 0)

    return tree


def _find_root_parent(source_parts, current_part):
    """
    Find the root parent of a given part by tracing back through the hierarchy.
    Returns the root part (the one that is not a child of any other part).
    """
    # If the current part is already a root part (not a child), return it
    if not current_part.is_child_part:
        return current_part

    # Find the parent of the current part
    parent_part = None
    for part in source_parts:
        # Check if this part is the parent of current_part
        if (current_part.parent_id and part.name == current_part.parent_id) or (
            not current_part.parent_id and part.part_number == current_part.parent_part
        ):
            parent_part = part
            break

    if not parent_part:
        # If no parent found, this might be an orphaned part or the root itself
        return current_part

    # Recursively find the root parent
    return _find_root_parent(source_parts, parent_part)


def _build_part_hierarchy(source_parts, root_part, parts_list):
    """
    Recursively build the complete hierarchy of parts starting from a root part.
    This includes the root part and all its children (direct and indirect).
    """
    # Add the current part to the list
    parts_list.append(root_part)

    # Find all direct children of the current part
    children = []
    for part in source_parts:
        if part.is_child_part and (
            (part.parent_id and part.parent_id == root_part.name)
            or (not part.parent_id and part.parent_part == root_part.part_number)
        ):
            children.append(part)

    # Recursively add children and their descendants
    for child in children:
        _build_part_hierarchy(source_parts, child, parts_list)


def _create_vlc_from_source(
    source_vlc_doc, target_vc_name, target_part_name, target_part_number
):
    """
    Create a new VLC document based on a source VLC document.
    """
    try:
        # Get the source VLC data
        source_data = source_vlc_doc.as_dict(no_nulls=True)

        # Remove fields that shouldn't be copied
        fields_to_remove = [
            "name",
            "creation",
            "modified",
            "modified_by",
            "owner",
            "docstatus",
            "idx",
            "velocetec_costing",
            "velocetec_costing_detail",
        ]

        for field in fields_to_remove:
            if field in source_data:
                del source_data[field]

        # Update with target information
        source_data["velocetec_costing"] = target_vc_name
        source_data["velocetec_costing_detail"] = target_part_name
        source_data["part_number"] = target_part_number

        # Create new VLC document
        new_vlc = frappe.new_doc("Velocetec Line Costing")
        new_vlc.update(source_data)

        # Insert the new VLC
        new_vlc.insert(ignore_permissions=True, ignore_mandatory=True)

        # Update the target VC detail with the VLC reference
        frappe.db.set_value(
            "Velocetec Costing Detail",
            target_part_name,
            "velocetec_line_costing",
            json.dumps(new_vlc.as_dict(no_nulls=True), default=str),
        )

        return new_vlc

    except Exception as e:
        frappe.log_error(
            f"Error creating VLC from source: {str(e)}", "VLC Creation Error"
        )
        return None


@frappe.whitelist()
def unlink_quotation(vc_docname):
    """Unlink a quotation from a Velocetec Costing document."""
    try:
        # Get the current quotation reference before unlinking
        vc_doc = frappe.get_doc("Velocetec Costing", vc_docname)
        current_quotation = vc_doc.reference_name

        # Update the document values directly in the database
        frappe.db.set_value(
            "Velocetec Costing",
            vc_docname,
            {
                "quotation_link_status": "UnLinked",
                "reference_doctype": "",
                "reference_name": "",
            },
        )

        # Add comment to track quotation unlinking
        if current_quotation:
            vc_doc.add_comment(
                "Info",
                f"Quotation {current_quotation} unlinked by {frappe.session.user}",
                comment_email=frappe.session.user,
            )

        # Return success
        return {"success": True, "message": "Quotation has been unlinked."}
    except Exception as e:
        frappe.log_error(f"Error unlinking quotation: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}

@frappe.whitelist()
def create_velocetec_costing_from_opportunity(opportunity_name):
    def postprocess(source, target):
        if source.opportunity_from == "Customer":
            target.customer = source.party_name
            target.customer_primary_contact = source.contact_person
        elif source.opportunity_from == "Lead":
            target.lead = source.party_name

    return get_mapped_doc(
        "Opportunity",
        opportunity_name,
        {
            "Opportunity": {
                "doctype": "Velocetec Costing",
                "field_map": {
                    "opportunity_from": "quotation_to",
                    "name": "reference_opportunity"
                },
            }
        },
        postprocess=postprocess
    ).insert(ignore_permissions=True)
