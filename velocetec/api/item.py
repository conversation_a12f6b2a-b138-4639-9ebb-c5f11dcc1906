#!/usr/bin/env python3
"""
API for Item Duplication with Dimensions
Handles creating duplicate items with specific dimensions and material sizes
"""

import frappe
from frappe import _
import json


@frappe.whitelist()
def duplicate_item_with_dimensions(source_item, base_item_name, material_form, dimensions):
    """
    Duplicate an item with specific dimensions and create/link to Velocetec Material Size
    
    Args:
        source_item (str): Name of the source item to duplicate
        base_item_name (str): Base name for the new item (without dimensions)
        material_form (str): "Block" or "Bar"
        dimensions (dict): Dictionary containing dimension values
    
    Returns:
        dict: Success status and new item details
    """
    try:
        # Parse dimensions if it's a string
        if isinstance(dimensions, str):
            dimensions = json.loads(dimensions)
        
        # Get the source item document
        source_doc = frappe.get_doc("Item", source_item)
        
        # Create dimension suffix and validate dimensions
        if material_form == "Block":
            x_dim = float(dimensions.get('x_dim', 0))
            y_dim = float(dimensions.get('y_dim', 0))
            z_dim = float(dimensions.get('z_dim', 0))
            
            if not all([x_dim, y_dim, z_dim]):
                return {"success": False, "error": "All block dimensions (X, Y, Z) are required"}
            
            dimension_suffix = f"{x_dim}x{y_dim}x{z_dim}"
            
        elif material_form == "Bar":
            d_dim = float(dimensions.get('d_dim', 0))
            l_dim = float(dimensions.get('l_dim', 0))
            
            if not all([d_dim, l_dim]):
                return {"success": False, "error": "All bar dimensions (D, L) are required"}
            
            dimension_suffix = f"{d_dim}x{l_dim}"
            
        else:
            return {"success": False, "error": "Invalid material form. Must be 'Block' or 'Bar'"}
        
        # Create new item code and name
        new_item_code = f"{base_item_name} - {dimension_suffix}"
        new_item_name = new_item_code
        
        # Check if item already exists
        if frappe.db.exists("Item", new_item_code):
            return {"success": False, "error": f"Item '{new_item_code}' already exists"}
        
        # Find or create Velocetec Material Size record
        material_size_name = get_or_create_material_size(material_form, dimensions)
        
        if not material_size_name:
            return {"success": False, "error": "Failed to create or find material size record"}
        
        # Create new item document
        new_item = frappe.copy_doc(source_doc)
        
        # Update the new item with specific values
        new_item.item_code = new_item_code
        new_item.item_name = new_item_name
        new_item.custom_material_size = material_size_name
        new_item.material_size = material_size_name  # Also populate the material_size field
        
        # Copy relevant fields from source item
        fields_to_copy = [
            'custom_material_form', 'has_batch_no', 'has_serial_no', 'serial_no_series',
            'item_group', 'stock_uom', 'is_stock_item', 'valuation_method',
            'description', 'brand', 'weight_per_unit', 'weight_uom'
        ]
        
        for field in fields_to_copy:
            if hasattr(source_doc, field) and getattr(source_doc, field):
                setattr(new_item, field, getattr(source_doc, field))
        
        # Update description to include dimensions
        if source_doc.description:
            new_item.description = f"{source_doc.description} - {dimension_suffix}"
        
        # Insert the new item
        new_item.insert(ignore_permissions=True)

        # Create batch for the dimensioned item if batch tracking is enabled
        batch_id = None
        if new_item.has_batch_no:
            from velocetec.utils.batch_utils import create_batch_for_dimensioned_item

            batch_id = create_batch_for_dimensioned_item(
                item_code=new_item_code,
                base_item_code=source_item,
                serial_no_series=source_doc.get("serial_no_series")
            )

            if not batch_id:
                frappe.log_error(
                    message=f"Failed to create batch for duplicated item {new_item_code}",
                    title="Item Duplication Batch Creation Failed"
                )

        return {
            "success": True,
            "new_item_code": new_item_code,
            "new_item_name": new_item_name,
            "material_size": material_size_name,
            "dimension_suffix": dimension_suffix,
            "batch_id": batch_id
        }
        
    except Exception as e:
        frappe.log_error(
            message=f"Error duplicating item: {str(e)}",
            title="Item Duplication Error"
        )
        return {"success": False, "error": str(e)}


def get_or_create_material_size(material_form, dimensions):
    """
    Find existing or create new Velocetec Material Size record
    
    Args:
        material_form (str): "Block" or "Bar"
        dimensions (dict): Dictionary containing dimension values
    
    Returns:
        str: Name of the material size record
    """
    try:
        if material_form == "Block":
            x_dim = str(float(dimensions.get('x_dim', 0)))
            y_dim = str(float(dimensions.get('y_dim', 0)))
            z_dim = str(float(dimensions.get('z_dim', 0)))
            
            # Check if material size already exists
            existing_size = frappe.db.get_value(
                "Velocetec Material Size",
                {
                    "material_form": "Block",
                    "x_dim": x_dim,
                    "y_dim": y_dim,
                    "z_dim": z_dim
                },
                "name"
            )
            
            if existing_size:
                return existing_size
            
            # Create new material size record
            new_size = frappe.get_doc({
                "doctype": "Velocetec Material Size",
                "material_form": "Block",
                "x_dim": x_dim,
                "y_dim": y_dim,
                "z_dim": z_dim
            })
            
        elif material_form == "Bar":
            d_dim = str(float(dimensions.get('d_dim', 0)))
            l_dim = str(float(dimensions.get('l_dim', 0)))
            
            # Check if material size already exists
            existing_size = frappe.db.get_value(
                "Velocetec Material Size",
                {
                    "material_form": "Bar",
                    "d_dim": d_dim,
                    "l_dim": l_dim
                },
                "name"
            )
            
            if existing_size:
                return existing_size
            
            # Create new material size record
            new_size = frappe.get_doc({
                "doctype": "Velocetec Material Size",
                "material_form": "Bar",
                "d_dim": d_dim,
                "l_dim": l_dim
            })
        
        else:
            return None
        
        # Insert the new material size record
        new_size.insert(ignore_permissions=True)
        return new_size.name
        
    except Exception as e:
        frappe.log_error(
            message=f"Error creating material size: {str(e)}",
            title="Material Size Creation Error"
        )
        return None


@frappe.whitelist()
def get_base_item_name(item_name):
    """
    Remove dimension suffixes from item name to get base name
    
    Args:
        item_name (str): Full item name with potential dimension suffix
    
    Returns:
        str: Base item name without dimension suffix
    """
    if not item_name:
        return ""
    
    # Remove dimension patterns like " - 30.0x20.0x10.0" or " - 25.0x100.0"
    import re
    base_name = re.sub(r'\s*-\s*[\d.]+x[\d.]+(?:x[\d.]+)?\s*$', '', item_name).strip()
    return base_name


@frappe.whitelist()
def validate_dimensions(material_form, dimensions):
    """
    Validate dimension values for the given material form
    
    Args:
        material_form (str): "Block" or "Bar"
        dimensions (dict): Dictionary containing dimension values
    
    Returns:
        dict: Validation result
    """
    try:
        if isinstance(dimensions, str):
            dimensions = json.loads(dimensions)
        
        if material_form == "Block":
            x_dim = dimensions.get('x_dim')
            y_dim = dimensions.get('y_dim')
            z_dim = dimensions.get('z_dim')
            
            if not all([x_dim, y_dim, z_dim]):
                return {"valid": False, "error": "All block dimensions (X, Y, Z) are required"}
            
            try:
                x_dim = float(x_dim)
                y_dim = float(y_dim)
                z_dim = float(z_dim)
                
                if any(dim <= 0 for dim in [x_dim, y_dim, z_dim]):
                    return {"valid": False, "error": "All dimensions must be positive numbers"}
                    
            except ValueError:
                return {"valid": False, "error": "All dimensions must be valid numbers"}
        
        elif material_form == "Bar":
            d_dim = dimensions.get('d_dim')
            l_dim = dimensions.get('l_dim')
            
            if not all([d_dim, l_dim]):
                return {"valid": False, "error": "All bar dimensions (D, L) are required"}
            
            try:
                d_dim = float(d_dim)
                l_dim = float(l_dim)
                
                if any(dim <= 0 for dim in [d_dim, l_dim]):
                    return {"valid": False, "error": "All dimensions must be positive numbers"}
                    
            except ValueError:
                return {"valid": False, "error": "All dimensions must be valid numbers"}
        
        else:
            return {"valid": False, "error": "Invalid material form"}
        
        return {"valid": True}
        
    except Exception as e:
        return {"valid": False, "error": str(e)}
