import frappe
import json


@frappe.whitelist()
def update_velocetec_costing_item(
    velocetec_costing,
    velocetec_costing_detail,
    machining,
    finishing,
    other,
    inspection,
    mass_finishing,
    subcontract,
    tool_making,
    turning,
    edm,
    design,
    material_price,
    fixings,
    sell_cost_each=None,
    delivery_each=None,
    ignore_timestamp=False,
):
    """Update the Velocetec Costing Item with the calculated values."""

    # Helper to cast or default
    def to_float(val):
        try:
            return float(val)
        except (TypeError, ValueError):
            return 0.0

    # Cast everything
    machining = to_float(machining)
    finishing = to_float(finishing)
    other = to_float(other)
    inspection = to_float(inspection)
    mass_finishing = to_float(mass_finishing)
    subcontract = to_float(subcontract)
    tool_making = to_float(tool_making)
    turning = to_float(turning)
    edm = to_float(edm)
    design = to_float(design)
    material_price = to_float(material_price)
    fixings = to_float(fixings)
    sell_cost_each = to_float(sell_cost_each)
    delivery_each = to_float(delivery_each)

    # Build a dictionary of fields to update
    updates = {
        "machining": machining,
        "finishing": finishing,
        "other": other,
        "inspection": inspection,
        "mass_finishing": mass_finishing,
        "sub_con": subcontract,
        "tool_making": tool_making,
        "turning": turning,
        "edm": edm,
        "design": design,
        "material_price": material_price,
        "fixings": fixings,
    }

    # Add sell_cost_each if provided
    if sell_cost_each is not None:
        updates["sell_cost_each"] = sell_cost_each

    # Add delivery_each if provided
    if delivery_each is not None:
        updates["delivery_each"] = delivery_each

    try:
        # Check if the document exists
        if not frappe.db.exists("Velocetec Costing Detail", velocetec_costing_detail):
            frappe.response["message"] = {
                "success": False,
                "message": f"Velocetec Costing Detail {velocetec_costing_detail} not found",
            }
            return

        # Use direct db_set_value to avoid conflicts
        for field, value in updates.items():
            frappe.db.set_value(
                "Velocetec Costing Detail",
                velocetec_costing_detail,
                field,
                value,
                update_modified=False if ignore_timestamp else True,
            )

        # Commit changes to database
        frappe.db.commit()

        # Return the parent Velocetec Costing name as confirmation
        frappe.response["message"] = {
            "success": True,
            "velocetec_costing": velocetec_costing,
        }
    except Exception as e:
        frappe.log_error(
            message=f"Error updating VCD {velocetec_costing_detail}: {str(e)}",
            title="VCD Update Error",
        )
        frappe.response["message"] = {"success": False, "message": f"Error: {str(e)}"}


@frappe.whitelist()
def save_and_validate_vlc(doc_data):
    """Save a Velocetec Line Costing document and trigger validation.

    Args:
        doc_data (str): JSON string of the document data

    Returns:
        dict: Updated document data
    """
    try:
        if isinstance(doc_data, str):
            doc_data = json.loads(doc_data)

        doc_name = doc_data.get("name")
        if not doc_name:
            return {"success": False, "message": "Document name is required"}

        # Get a fresh copy of the document to avoid conflicts
        doc = frappe.get_doc("Velocetec Line Costing", doc_name)

        # Check if parent VC is linked to a quotation
        if doc.velocetec_costing:
            link_status = frappe.db.get_value(
                "Velocetec Costing", doc.velocetec_costing, "quotation_link_status"
            )
            if link_status == "Linked":
                # For linked documents, just return the current document without saving
                # This allows navigation back to the parent VC without triggering validation errors
                return {
                    "success": True,
                    "message": "Document is linked to a quotation. No changes were made.",
                    "doc": doc.as_dict(),
                }

        # Update only the fields that have changed
        for key, value in doc_data.items():
            if key in doc.meta.get_fieldnames_with_value() and key not in [
                "modified",
                "creation",
                "owner",
                "modified_by",
            ]:
                doc.set(key, value)

        # Save with flags to avoid conflicts
        doc.flags.ignore_validate_update_after_submit = True
        doc.flags.ignore_validate = True
        doc.flags.ignore_version = True
        doc.save(ignore_permissions=True, ignore_version=True)

        # Run validation separately to update calculations
        frappe.flags.allow_linked_vlc_changes = True
        doc.validate()
        frappe.flags.allow_linked_vlc_changes = False

        # Update the parent VCD with the latest VLC data
        if doc.velocetec_costing_detail:
            # Update the VCD with all the necessary fields
            updates = {
                "velocetec_line_costing": json.dumps(
                    doc.as_dict(no_nulls=1), default=str
                ),
                "material_price": doc.material_price,
                "machining": doc.machining,
                "finishing": doc.finishing,
                "inspection": doc.inspection,
                "mass_finishing": doc.mass_finishing,
                "other": doc.other,
                "sub_con": doc.sub_con,
                "tool_making": doc.tool_making,
                "turning": doc.turning,
                "design": doc.design,
                "edm": doc.edm,
                "assembly": doc.assembly,
                "fixings": doc.total_fixings_cost,
            }

            # Instead of calculating raw_total_cost here, use the value from the VLC document
            # This ensures we get the correct value that includes special handling for Inspection and Mass Finishing
            # The VLC validate method already calculates this correctly
            raw_total_cost = 0

            # Check if we have raw cost values from the VLC validation
            # These will include the special handling for Inspection and Mass Finishing
            try:
                # Get raw material costs (without markup)
                # Note: We need to calculate raw material costs from the individual rows
                # since block_material_amount and bar_material_amount now store price values (with markup)
                raw_material_cost = sum(
                    row.amount or 0 for row in doc.line_material_details or [] if row.x and row.y and row.z
                ) + sum(
                    row.amount or 0 for row in doc.bar_material_details or [] if row.d and row.l
                )

                # Get raw fixings cost
                raw_fixings_cost = sum(
                    (row.qty or 0) * (row.costunit or 0)
                    for row in doc.line_fixings or []
                )

                # Get raw workstation costs - these already have the special handling applied
                # For Inspection and Mass Finishing, the VLC validation method applies the special logic
                raw_total_cost = sum(
                    [
                        raw_material_cost,
                        doc.machining or 0,
                        doc.design or 0,
                        doc.finishing or 0,
                        doc.inspection
                        or 0,  # This already has the special inspection qty handling
                        doc.other or 0,
                        doc.mass_finishing
                        or 0,  # This already has the special mass finishing loads handling
                        doc.sub_con or 0,
                        raw_fixings_cost,
                        doc.tool_making or 0,
                        doc.turning or 0,
                        doc.edm or 0,
                        doc.assembly or 0,
                    ]
                )
            except Exception as e:
                frappe.log_error(
                    f"Error calculating raw_total_cost: {str(e)}", "VLC Save Error"
                )
                # Fallback to simple sum if there's an error
                # Calculate raw material costs from individual rows
                raw_block_material = sum(
                    row.amount or 0 for row in doc.line_material_details or [] if row.x and row.y and row.z
                )
                raw_bar_material = sum(
                    row.amount or 0 for row in doc.bar_material_details or [] if row.d and row.l
                )
                raw_fixings = sum(
                    (row.qty or 0) * (row.costunit or 0) for row in doc.line_fixings or []
                )

                raw_total_cost = sum(
                    [
                        raw_block_material,
                        raw_bar_material,
                        doc.machining or 0,
                        doc.design or 0,
                        doc.finishing or 0,
                        doc.inspection or 0,
                        doc.other or 0,
                        doc.mass_finishing or 0,
                        doc.sub_con or 0,
                        raw_fixings,
                        doc.tool_making or 0,
                        doc.turning or 0,
                        doc.edm or 0,
                        doc.assembly or 0,
                    ]
                )

            # Add sell_cost_each to the updates
            updates["sell_cost_each"] = raw_total_cost

            # Update the VCD with all fields at once
            frappe.db.set_value(
                "Velocetec Costing Detail",
                doc.velocetec_costing_detail,
                updates,
                update_modified=True,
            )

            # Force a recalculation of the parent VC
            if doc.velocetec_costing:
                try:
                    # Get the parent VC document
                    vc_doc = frappe.get_doc("Velocetec Costing", doc.velocetec_costing)

                    # Mark all items as needing recalculation
                    for item in vc_doc.items:
                        item.needs_recalculation = True

                    # Save the document to trigger recalculation
                    vc_doc.save(ignore_permissions=True)
                except Exception as e:
                    frappe.log_error(
                        f"Error updating parent VC: {str(e)}", "VLC Save Error"
                    )

        # Commit changes to database
        frappe.db.commit()

        # Return the updated document
        return {
            "success": True,
            "message": "Document saved and validated successfully",
            "doc": doc.as_dict(),
        }
    except Exception as e:
        frappe.log_error(f"Error in save_and_validate_vlc: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}
