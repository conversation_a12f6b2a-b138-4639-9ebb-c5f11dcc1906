[{"name": "BOM-main-links_order", "owner": "Administrator", "creation": "2025-07-08 16:26:47.645562", "modified": "2025-07-08 16:26:47.645562", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "BOM", "property": "links_order", "property_type": "Small Text", "value": "[\"dpcdq7lj3h\"]", "doctype": "Property Setter"}, {"name": "BOM-main-field_order", "owner": "Administrator", "creation": "2025-07-08 16:26:47.597299", "modified": "2025-07-08 16:26:47.597299", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "BOM", "property": "field_order", "property_type": "Data", "value": "[\"production_item_tab\", \"item\", \"company\", \"uom\", \"quantity\", \"cb0\", \"is_active\", \"is_default\", \"allow_alternative_item\", \"set_rate_of_sub_assembly_item_based_on_bom\", \"project\", \"image\", \"currency_detail\", \"rm_cost_as_per\", \"buying_price_list\", \"price_list_currency\", \"plc_conversion_rate\", \"column_break_ivyw\", \"currency\", \"conversion_rate\", \"materials_section\", \"items\", \"section_break_21\", \"operations_section_section\", \"with_operations\", \"column_break_23\", \"transfer_material_against\", \"routing\", \"fg_based_operating_cost\", \"fg_based_section_section\", \"operating_cost_per_bom_quantity\", \"operations_section\", \"operations\", \"scrap_section\", \"scrap_items_section\", \"scrap_items\", \"process_loss_section\", \"process_loss_percentage\", \"column_break_ssj2\", \"process_loss_qty\", \"costing\", \"operating_cost\", \"raw_material_cost\", \"scrap_material_cost\", \"cb1\", \"base_operating_cost\", \"base_raw_material_cost\", \"base_scrap_material_cost\", \"column_break_26\", \"total_cost\", \"base_total_cost\", \"more_info_tab\", \"item_name\", \"description\", \"column_break_27\", \"has_variants\", \"quality_inspection_section_break\", \"inspection_required\", \"column_break_dxp7\", \"quality_inspection_template\", \"section_break0\", \"exploded_items\", \"website_section\", \"show_in_website\", \"route\", \"column_break_52\", \"website_image\", \"thumbnail\", \"sb_web_spec\", \"show_items\", \"show_operations\", \"web_long_description\", \"reference_section\", \"bom_creator\", \"bom_creator_item\", \"column_break_oxbz\", \"amended_from\", \"connections_tab\", \"custom_velocetec_line_costing\", \"custom_sales_order\"]", "doctype": "Property Setter"}, {"name": "Sales Order-main-field_order", "owner": "Administrator", "creation": "2025-07-08 16:26:31.382824", "modified": "2025-07-08 16:26:31.382824", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "Sales Order", "property": "field_order", "property_type": "Data", "value": "[\"customer_section\", \"column_break0\", \"title\", \"naming_series\", \"customer\", \"customer_name\", \"tax_id\", \"order_type\", \"column_break_7\", \"transaction_date\", \"delivery_date\", \"velocetec_costing\", \"opportunity\", \"column_break1\", \"po_no\", \"po_date\", \"company\", \"skip_delivery_note\", \"amended_from\", \"ajtest\", \"accounting_dimensions_section\", \"cost_center\", \"dimension_col_break\", \"project\", \"currency_and_price_list\", \"currency\", \"conversion_rate\", \"column_break2\", \"selling_price_list\", \"price_list_currency\", \"plc_conversion_rate\", \"ignore_pricing_rule\", \"sec_warehouse\", \"scan_barcode\", \"column_break_28\", \"set_warehouse\", \"reserve_stock\", \"items_section\", \"items\", \"section_break_31\", \"total_qty\", \"total_net_weight\", \"column_break_33\", \"base_total\", \"base_net_total\", \"column_break_33a\", \"total\", \"net_total\", \"taxes_section\", \"tax_category\", \"taxes_and_charges\", \"column_break_38\", \"shipping_rule\", \"delivery_type\", \"column_break_49\", \"incoterm\", \"named_place\", \"section_break_40\", \"taxes\", \"section_break_43\", \"base_total_taxes_and_charges\", \"column_break_46\", \"total_taxes_and_charges\", \"totals\", \"base_grand_total\", \"base_rounding_adjustment\", \"base_rounded_total\", \"base_in_words\", \"column_break3\", \"grand_total\", \"rounding_adjustment\", \"rounded_total\", \"in_words\", \"advance_paid\", \"disable_rounded_total\", \"section_break_48\", \"apply_discount_on\", \"base_discount_amount\", \"coupon_code\", \"column_break_50\", \"additional_discount_percentage\", \"discount_amount\", \"sec_tax_breakup\", \"other_charges_calculation\", \"packing_list\", \"packed_items\", \"pricing_rule_details\", \"pricing_rules\", \"contact_info\", \"billing_address_column\", \"customer_address\", \"address_display\", \"customer_group\", \"territory\", \"column_break_84\", \"contact_person\", \"contact_display\", \"contact_phone\", \"contact_mobile\", \"contact_email\", \"shipping_address_column\", \"shipping_address_name\", \"shipping_address\", \"column_break_93\", \"dispatch_address_name\", \"dispatch_address\", \"col_break46\", \"company_address\", \"company_address_display\", \"column_break_92\", \"company_contact_person\", \"payment_schedule_section\", \"payment_terms_section\", \"payment_terms_template\", \"payment_schedule\", \"terms_section_break\", \"tc_name\", \"terms\", \"more_info\", \"section_break_78\", \"status\", \"delivery_status\", \"per_delivered\", \"column_break_81\", \"per_billed\", \"per_picked\", \"billing_status\", \"sales_team_section_break\", \"sales_partner\", \"column_break7\", \"amount_eligible_for_commission\", \"commission_rate\", \"total_commission\", \"section_break1\", \"sales_team\", \"loyalty_points_redemption\", \"loyalty_points\", \"column_break_116\", \"loyalty_amount\", \"subscription_section\", \"from_date\", \"to_date\", \"column_break_108\", \"auto_repeat\", \"update_auto_repeat_reference\", \"printing_details\", \"letter_head\", \"group_same_items\", \"column_break4\", \"select_print_heading\", \"language\", \"additional_info_section\", \"is_internal_customer\", \"represents_company\", \"column_break_152\", \"source\", \"inter_company_order_reference\", \"campaign\", \"party_account_currency\", \"connections_tab\", \"bom\"]", "doctype": "Property Setter"}]