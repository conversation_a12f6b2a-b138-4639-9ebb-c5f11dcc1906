[{"name": "Quotation-velocetec_costing", "owner": "<EMAIL>", "creation": "2025-03-26 08:52:11.169755", "modified": "2025-03-26 08:54:48.686234", "modified_by": "<EMAIL>", "docstatus": 0, "idx": 10, "is_system_generated": 0, "dt": "Quotation", "label": "Velocetec Costing", "fieldname": "velocetec_costing", "insert_after": "valid_till", "length": 0, "fieldtype": "Link", "precision": "", "hide_seconds": 0, "hide_days": 0, "options": "Velocetec Costing", "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 1, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 1, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-04-18T11:39:06.097Z"}, {"name": "Quotation Item-delivery_date", "owner": "Administrator", "creation": "2025-04-21 10:33:44.644771", "modified": "2025-04-21 10:47:32.923255", "modified_by": "Administrator", "docstatus": 0, "idx": 4, "is_system_generated": 0, "dt": "Quotation Item", "label": "Delivery Date", "fieldname": "delivery_date", "insert_after": "item_name", "length": 0, "fieldtype": "Date", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-04-21T09:47:34.336Z"}, {"name": "Quotation-delivery_type", "owner": "Administrator", "creation": "2025-06-09 15:19:06.418445", "modified": "2025-06-09 15:19:27.959666", "modified_by": "Administrator", "docstatus": 0, "idx": 42, "is_system_generated": 0, "dt": "Quotation", "label": "Delivery Type", "fieldname": "delivery_type", "insert_after": "shipping_rule", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 1, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-06-09T14:19:29.227Z"}, {"name": "Quotation Item-lead_time", "owner": "Administrator", "creation": "2025-06-13 13:58:51.946919", "modified": "2025-06-13 13:58:51.946919", "modified_by": "Administrator", "docstatus": 0, "idx": 5, "is_system_generated": 0, "dt": "Quotation Item", "label": "Lead Time", "fieldname": "lead_time", "insert_after": "delivery_date", "length": 0, "fieldtype": "Duration", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 1, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-06-13T12:59:01.418Z"}, {"name": "Quotation-customer_reference", "owner": "Administrator", "creation": "2025-06-16 13:16:39.788104", "modified": "2025-06-16 13:16:39.788104", "modified_by": "Administrator", "docstatus": 0, "idx": 12, "is_system_generated": 0, "dt": "Quotation", "label": "Customer Reference", "fieldname": "customer_reference", "insert_after": "velocetec_costing", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_from": "opportunity.customer_reference", "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-06-16T12:17:12.119Z"}]